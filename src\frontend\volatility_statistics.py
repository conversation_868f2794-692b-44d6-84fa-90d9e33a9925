"""
Volatility Statistics Tab
Placeholder for future volatility analysis functionality.
"""

from PyQt6.QtWidgets import <PERSON>Widget, QVBoxLayout, QHBoxLayout, QLabel, QTabWidget, QSplitter, QRadioButton, QButtonGroup, QPushButton, QTableView, QAbstractItemView, QStackedWidget, QLineEdit, QComboBox, QCheckBox, QDialog, QTextEdit
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor
from typing import Dict, Any
import pyqtgraph as pg
import numpy as np
from scipy.interpolate import CubicSpline, splrep, splev

# Import the table model from data_tab
from src.frontend.data_tab import MarketDataTableModel


class ExportToTradingViewDialog(QDialog):
    """Dialog for exporting PineScript code to TradingView."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Export to TradingView")
        self.setModal(True)
        self.resize(900, 700)

        # Apply dark theme matching other dialogs
        self.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 10pt;
                padding: 8px;
            }
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 8px 16px;
                font-size: 10pt;
                border-radius: 3px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
            QLabel {
                color: #ffffff;
                font-size: 11pt;
            }
        """)

        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout()
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # Title and instructions
        title_label = QLabel("TradingView PineScript Export")
        title_label.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)

        instructions_label = QLabel("Copy the PineScript code below and paste it into TradingView's Pine Editor:")
        instructions_label.setFont(QFont("Segoe UI", 10))
        instructions_label.setStyleSheet("color: #cccccc; margin-bottom: 5px;")
        layout.addWidget(instructions_label)

        # Text viewer for PineScript code
        self.text_viewer = QTextEdit()
        self.text_viewer.setReadOnly(True)
        self.text_viewer.setPlaceholderText("PineScript code will appear here...")
        layout.addWidget(self.text_viewer)

        # Button layout
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # Copy to Clipboard button
        copy_button = QPushButton("Copy to Clipboard")
        copy_button.clicked.connect(self._copy_to_clipboard)
        button_layout.addWidget(copy_button)

        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def set_pinescript_content(self, pinescript_code):
        """Set the PineScript content in the text viewer."""
        self.text_viewer.setPlainText(pinescript_code)

    def _copy_to_clipboard(self):
        """Copy the PineScript code to clipboard."""
        try:
            from PyQt6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(self.text_viewer.toPlainText())

            # Show brief confirmation
            from PyQt6.QtWidgets import QMessageBox
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setWindowTitle("Copied")
            msg_box.setText("PineScript code copied to clipboard!")
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.exec()

        except Exception as e:
            print(f"Error copying to clipboard: {e}")


class VolatilityChart(pg.PlotWidget):
    """Empty Volatility Graph chart widget."""

    def __init__(self):
        super().__init__()
        self.setBackground('#1e1e1e')
        self.showGrid(x=False, y=False)  # Turn off grid

        # Remove X and Y axes
        self.hideAxis('left')
        self.hideAxis('bottom')

        # Removed vertical reference lines as requested

    def calculate_arrow_sizes(self, projected_highs, projected_lows, axis_limits=None):
        """Calculate dynamic arrow sizes based on the actual displayed range.

        Returns:
            tuple: (arrow_y_size, arrow_x_size) where:
                - arrow_y_size is 3% of the displayed height range (from axis limits)
                - arrow_x_size is 0.5% of the visual box range
        """
        try:
            if not projected_highs or not projected_lows:
                return 1.5, 0.05  # Fallback to fixed sizes

            # Use axis limits if provided (for proper scaling), otherwise fall back to data range
            if axis_limits and 'y_min' in axis_limits and 'y_max' in axis_limits:
                # Calculate range based on actual displayed axis range
                displayed_range = axis_limits['y_max'] - axis_limits['y_min']
                # Arrow height: 3% of the displayed range
                arrow_y_size = displayed_range * 0.03
            else:
                # Fallback to data range calculation
                highest_high = max(projected_highs)
                lowest_low = min(projected_lows)
                total_range = highest_high - lowest_low
                # Arrow height: 3% of the total price range
                arrow_y_size = total_range * 0.03

            # Arrow width: 0.5% of visual box range (VolatilityChart: -4x to 4x = 8 units)
            arrow_x_size = 8 * 0.005  # 0.5% of 8 = 0.04

            return arrow_y_size, arrow_x_size

        except Exception as e:
            print(f"Error calculating arrow sizes: {e}")
            import traceback
            traceback.print_exc()
            return 1.5, 0.05  # Fallback to fixed sizes

    def update_data(self, filtered_high_data, filtered_low_data, market_data=None):
        """Update volatility chart with filtered data and market data from backend."""
        # Clear existing plot items
        self.clear()

        if not market_data:
            return

        # Get the data service from the parent tab to perform calculations
        parent_tab = self.parent()
        while parent_tab and not hasattr(parent_tab, 'data_service'):
            parent_tab = parent_tab.parent()

        if not parent_tab or not hasattr(parent_tab, 'data_service') or not parent_tab.data_service:
            print("No data service available for volatility calculations")
            return

        # Use backend service to calculate all data
        calc_data = parent_tab.data_service.calculate_volatility_data(
            filtered_high_data, filtered_low_data, market_data
        )

        if not calc_data:
            print("Failed to calculate volatility data")
            return

        # Extract calculated values
        title_info = calc_data.get('title_info', {})
        current_close = calc_data.get('current_close')
        max_high = calc_data.get('max_high')
        max_low = calc_data.get('max_low')

        # Debug: Check what current_close the backend calculated
        print(f"VOLATILITY CHART DEBUG: Backend calculated current_close = ${current_close:.2f}" if current_close else "VOLATILITY CHART DEBUG: Backend returned None for current_close")
        apex = calc_data.get('apex')
        apex_high_mean = calc_data.get('apex_high_mean')
        apex_low_mean = calc_data.get('apex_low_mean')
        highs_stats = calc_data.get('highs_stats', {})
        lows_stats = calc_data.get('lows_stats', {})
        all_highs = calc_data.get('all_highs', [])
        all_lows = calc_data.get('all_lows', [])
        axis_limits = calc_data.get('axis_limits', {})

        # Calculate arrow sizes using the actual displayed axis range for proper scaling
        arrow_y_size, arrow_x_size = self.calculate_arrow_sizes(all_highs, all_lows, axis_limits)
        title_position = calc_data.get('title_position', {})

        # Add title to top left
        try:
            if title_info:
                title_text = title_info.get('title_text', '')
                title_label = pg.TextItem(title_text, color='white', anchor=(0, 0))
                title_label.setFont(QFont("Segoe UI", 8))
                title_label.setPos(-3.5, 0)  # Will be repositioned later
                self.addItem(title_label)
                self.title_label = title_label

        except Exception as e:
            print(f"Error adding title to volatility chart: {e}")

        # Add current close price line and label
        try:
            if current_close is not None:
                # Add a finite grey 2pt line from -1 to 1 at the close price level
                self.plot(
                    [-1, 1], [current_close, current_close],
                    pen=pg.mkPen(color='grey', width=2),
                    name=f'Close: {current_close:.2f}'
                )

                # Add label for Current Price - above the line, left aligned
                current_price_label = pg.TextItem(f'Current Price:\n${current_close:.2f}', color='white', anchor=(0, 1))
                current_price_label.setPos(-1, current_close)  # Directly above the line
                self.addItem(current_price_label)

        except Exception as e:
            print(f"Error adding close price line to volatility chart: {e}")

        # All calculations are now done by the backend service
        # The calculated values are already extracted above

        # Add Max_High dot at 3x position (white) with label
        try:
            if max_high is not None:
                self.plot(
                    [3], [max_high],
                    pen=None,
                    symbol='o',
                    symbolBrush='white',
                    symbolSize=8,
                    name=f'Max_High: {max_high:.2f}'
                )

                # Add label for Max_High
                max_high_label = pg.TextItem(f'Max: ${max_high:.2f}', color='white', anchor=(0, 0.5))
                max_high_label.setPos(3.1, max_high)
                self.addItem(max_high_label)

        except Exception as e:
            print(f"Error adding Max_High dot to volatility chart: {e}")

        # Add Max_Low dot at -3x position (white) with label
        try:
            if max_low is not None:
                self.plot(
                    [-3], [max_low],
                    pen=None,
                    symbol='o',
                    symbolBrush='white',
                    symbolSize=8,
                    name=f'Max_Low: {max_low:.2f}'
                )

                # Add label for Max_Low
                max_low_label = pg.TextItem(f'Max: ${max_low:.2f}', color='white', anchor=(1, 0.5))
                max_low_label.setPos(-3.1, max_low)
                self.addItem(max_low_label)

        except Exception as e:
            print(f"Error adding Max_Low dot to volatility chart: {e}")

        # Add apex dot at 0x position (white)
        try:
            if apex is not None:
                self.plot(
                    [0], [apex],
                    pen=None,
                    symbol='o',
                    symbolBrush='white',
                    symbolSize=8,
                    name=f'Apex: {apex:.2f}'
                )
        except Exception as e:
            print(f"Error adding Apex dot to volatility chart: {e}")

        # Add mean of apex and Max_Low at -1x position (white)
        try:
            if apex_low_mean is not None:
                self.plot(
                    [-1], [apex_low_mean],
                    pen=None,
                    symbol='o',
                    symbolBrush='white',
                    symbolSize=8,
                    name=f'Apex-Low: {apex_low_mean:.2f}'
                )
        except Exception as e:
            print(f"Error adding Apex-Low mean dot to volatility chart: {e}")

        # Add mean of apex and Max_High at 1x position (white)
        try:
            if apex_high_mean is not None:
                self.plot(
                    [1], [apex_high_mean],
                    pen=None,
                    symbol='o',
                    symbolBrush='white',
                    symbolSize=8,
                    name=f'Apex-High: {apex_high_mean:.2f}'
                )
        except Exception as e:
            print(f"Error adding Apex-High mean dot to volatility chart: {e}")

        # Add statistical dots for highs at 3x position
        if highs_stats:

            # MaxAvg: highest 50% highs average
            try:
                if 'max_avg' in highs_stats:
                    self.plot(
                        [3], [highs_stats['max_avg']],
                        pen=None,
                        symbol='o',
                        symbolBrush='white',
                        symbolSize=8,
                        name=f'MaxAvg_H: {highs_stats["max_avg"]:.2f}'
                    )

                    # Add label for MaxAvg_H
                    max_avg_high_label = pg.TextItem(f'MaxAvg: ${highs_stats["max_avg"]:.2f}', color='white', anchor=(0, 0.5))
                    max_avg_high_label.setPos(3.1, highs_stats['max_avg'])
                    self.addItem(max_avg_high_label)

                    # Add light blue horizontal line from MaxAvg_H to 0x
                    self.plot(
                        [3, 0], [highs_stats['max_avg'], highs_stats['max_avg']],
                        pen=pg.mkPen(color='lightblue', width=2),  # Changed to 2pt
                        name=f'MaxAvg_H_Line: {highs_stats["max_avg"]:.2f}'
                    )
            except Exception as e:
                print(f"Error adding MaxAvg highs dot: {e}")

            # Average: Average of all highs
            try:
                if 'average' in highs_stats:
                    self.plot(
                        [3], [highs_stats['average']],
                        pen=None,
                        symbol='o',
                        symbolBrush='white',
                        symbolSize=8,
                        name=f'Avg_H: {highs_stats["average"]:.2f}'
                    )

                    # Add label for Avg_H
                    avg_high_label = pg.TextItem(f'Avg: ${highs_stats["average"]:.2f}', color='white', anchor=(0, 0.5))
                    avg_high_label.setPos(3.1, highs_stats['average'])
                    self.addItem(avg_high_label)

            except Exception as e:
                print(f"Error adding Average highs dot: {e}")

            # Median: Median of all highs - dark blue horizontal line from 0.5x to 0x
            try:
                if 'median' in highs_stats:
                    self.plot(
                        [0.5, 0], [highs_stats['median'], highs_stats['median']],
                        pen=pg.mkPen(color='darkblue', width=4),  # Quadruple width of white lines
                        name=f'Med_H: {highs_stats["median"]:.2f}'
                    )

                    # Add label for Median High - above the line, centered left
                    median_high_label = pg.TextItem(f'Median:\n${highs_stats["median"]:.2f}', color='white', anchor=(0, 1))
                    median_high_label.setPos(0.25, highs_stats['median'])  # Directly above the line
                    self.addItem(median_high_label)

                    # Add vertical line from median high to current price at 0x
                    if current_close is not None:
                        self.plot(
                            [0, 0], [highs_stats['median'], current_close],
                            pen=pg.mkPen(color='darkblue', width=4),  # Same color and width as horizontal
                            name=f'Med_H_Vertical: {highs_stats["median"]:.2f} to {current_close:.2f}'
                        )
            except Exception as e:
                print(f"Error adding Median highs line: {e}")

            # MinAvg: lowest 50% highs average
            try:
                if 'min_avg' in highs_stats:
                    self.plot(
                        [3], [highs_stats['min_avg']],
                        pen=None,
                        symbol='o',
                        symbolBrush='white',
                        symbolSize=8,
                        name=f'MinAvg_H: {highs_stats["min_avg"]:.2f}'
                    )

                    # Add label for MinAvg_H
                    min_avg_high_label = pg.TextItem(f'MinAvg: ${highs_stats["min_avg"]:.2f}', color='white', anchor=(0, 0.5))
                    min_avg_high_label.setPos(3.1, highs_stats['min_avg'])
                    self.addItem(min_avg_high_label)

            except Exception as e:
                print(f"Error adding MinAvg highs dot: {e}")

        # Add statistical dots for lows at -3x position
        if lows_stats:
            # MinAvg: highest 50% average low
            try:
                if 'min_avg' in lows_stats:
                    self.plot(
                        [-3], [lows_stats['min_avg']],
                        pen=None,
                        symbol='o',
                        symbolBrush='white',
                        symbolSize=8,
                        name=f'MinAvg_L: {lows_stats["min_avg"]:.2f}'
                    )

                    # Add label for MinAvg_L (min_avg contains highest 50% average low, so label as MinAvg)
                    min_avg_low_label = pg.TextItem(f'MinAvg: ${lows_stats["min_avg"]:.2f}', color='white', anchor=(1, 0.5))
                    min_avg_low_label.setPos(-3.1, lows_stats['min_avg'])
                    self.addItem(min_avg_low_label)

            except Exception as e:
                print(f"Error adding MinAvg lows dot: {e}")

            # Average: Average of all lows
            try:
                if 'average' in lows_stats:
                    self.plot(
                        [-3], [lows_stats['average']],
                        pen=None,
                        symbol='o',
                        symbolBrush='white',
                        symbolSize=8,
                        name=f'Avg_L: {lows_stats["average"]:.2f}'
                    )

                    # Add label for Avg_L
                    avg_low_label = pg.TextItem(f'Avg: ${lows_stats["average"]:.2f}', color='white', anchor=(1, 0.5))
                    avg_low_label.setPos(-3.1, lows_stats['average'])
                    self.addItem(avg_low_label)

            except Exception as e:
                print(f"Error adding Average lows dot: {e}")

            # Median: Median of all lows - dark red horizontal line from -0.5x to 0x
            try:
                if 'median' in lows_stats:
                    self.plot(
                        [-0.5, 0], [lows_stats['median'], lows_stats['median']],
                        pen=pg.mkPen(color='darkred', width=4),  # Quadruple width of white lines
                        name=f'Med_L: {lows_stats["median"]:.2f}'
                    )

                    # Add label for Median Low - below the line, left aligned to the left of its line
                    median_low_label = pg.TextItem(f'Median:\n${lows_stats["median"]:.2f}', color='white', anchor=(0, 0))
                    median_low_label.setPos(-0.5, lows_stats['median'])  # At the left end of the line (-0.5x), below
                    self.addItem(median_low_label)

                    # Add vertical line from median low to current price at 0x
                    if current_close is not None:
                        self.plot(
                            [0, 0], [lows_stats['median'], current_close],
                            pen=pg.mkPen(color='darkred', width=4),  # Same color and width as horizontal
                            name=f'Med_L_Vertical: {lows_stats["median"]:.2f} to {current_close:.2f}'
                        )
            except Exception as e:
                print(f"Error adding Median lows line: {e}")

            # MaxAvg: lowest 50% average low
            try:
                if 'max_avg' in lows_stats:
                    self.plot(
                        [-3], [lows_stats['max_avg']],
                        pen=None,
                        symbol='o',
                        symbolBrush='white',
                        symbolSize=8,
                        name=f'MaxAvg_L: {lows_stats["max_avg"]:.2f}'
                    )

                    # Add label for MaxAvg_L (max_avg contains lowest 50% average low, so label as MaxAvg)
                    max_avg_low_label = pg.TextItem(f'MaxAvg: ${lows_stats["max_avg"]:.2f}', color='white', anchor=(1, 0.5))
                    max_avg_low_label.setPos(-3.1, lows_stats['max_avg'])
                    self.addItem(max_avg_low_label)

                    # Add light red horizontal line from MaxAvg_L to 0x (max_avg contains MaxAvg for lows)
                    self.plot(
                        [-3, 0], [lows_stats['max_avg'], lows_stats['max_avg']],
                        pen=pg.mkPen(color='lightcoral', width=2),  # Changed to 2pt
                        name=f'MaxAvg_L_Line: {lows_stats["max_avg"]:.2f}'
                    )
            except Exception as e:
                print(f"Error adding MaxAvg lows dot: {e}")

        # Add connecting lines
        try:
            # Connect apex to apex_high
            if apex is not None and apex_high_mean is not None:
                self.plot(
                    [0, 1], [apex, apex_high_mean],
                    pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                    name='Apex-ApexHigh Line'
                )

            # Connect apex to apex_low
            if apex is not None and apex_low_mean is not None:
                self.plot(
                    [0, -1], [apex, apex_low_mean],
                    pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                    name='Apex-ApexLow Line'
                )

            # Connect apex_high to all dots on 3x except median
            if apex_high_mean is not None and max_high is not None and highs_stats:
                # Connect to Max_High
                if max_high is not None:
                    self.plot(
                        [1, 3], [apex_high_mean, max_high],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexHigh-MaxHigh Line'
                    )

                # Connect to MaxAvg_H (highest 50% highs average)
                if 'max_avg' in highs_stats:
                    self.plot(
                        [1, 3], [apex_high_mean, highs_stats['max_avg']],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexHigh-MaxAvgH Line'
                    )

                # Connect to Avg_H (average of all highs)
                if 'average' in highs_stats:
                    self.plot(
                        [1, 3], [apex_high_mean, highs_stats['average']],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexHigh-AvgH Line'
                    )

                # Connect to MinAvg_H (lowest 50% highs average)
                if 'min_avg' in highs_stats:
                    self.plot(
                        [1, 3], [apex_high_mean, highs_stats['min_avg']],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexHigh-MinAvgH Line'
                    )

                # Note: Not connecting to median as requested

        except Exception as e:
            print(f"Error adding apex_high connecting lines: {e}")

        try:
            # Connect apex_low to all dots on -3x except median
            if apex_low_mean is not None and max_low is not None and lows_stats:
                # Connect to Max_Low
                if max_low is not None:
                    self.plot(
                        [-1, -3], [apex_low_mean, max_low],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexLow-MaxLow Line'
                    )

                # Connect to MinAvg_L (highest 50% average low)
                if 'min_avg' in lows_stats:
                    self.plot(
                        [-1, -3], [apex_low_mean, lows_stats['min_avg']],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexLow-MinAvgL Line'
                    )

                # Connect to Avg_L (average of all lows)
                if 'average' in lows_stats:
                    self.plot(
                        [-1, -3], [apex_low_mean, lows_stats['average']],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexLow-AvgL Line'
                    )

                # Connect to MaxAvg_L (lowest 50% average low)
                if 'max_avg' in lows_stats:
                    self.plot(
                        [-1, -3], [apex_low_mean, lows_stats['max_avg']],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexLow-MaxAvgL Line'
                    )

                # Note: Not connecting to median as requested

        except Exception as e:
            print(f"Error adding apex_low connecting lines: {e}")

        # Add white vertical line at 0x from max low to max high with arrows at both ends
        try:
            if max_low is not None and max_high is not None:
                # Create the main vertical line
                self.plot(
                    [0, 0], [max_low, max_high],
                    pen=pg.mkPen(color='white', width=2),
                    name=f'Vertical Range: {max_low:.2f} to {max_high:.2f}'
                )

                # Add arrow at top (pointing up) - filled, dynamic sizing
                self.plot(
                    [0, -arrow_x_size, arrow_x_size, 0], [max_high, max_high - arrow_y_size, max_high - arrow_y_size, max_high],
                    pen=pg.mkPen(color='white', width=1),
                    brush=pg.mkBrush(color='white'),
                    fillLevel=0,
                    name='Top Arrow'
                )

                # Add arrow at bottom (pointing down) - filled, dynamic sizing
                self.plot(
                    [0, -arrow_x_size, arrow_x_size, 0], [max_low, max_low + arrow_y_size, max_low + arrow_y_size, max_low],
                    pen=pg.mkPen(color='white', width=1),
                    brush=pg.mkBrush(color='white'),
                    fillLevel=0,
                    name='Bottom Arrow'
                )

        except Exception as e:
            print(f"Error adding vertical line with arrows: {e}")

        # Set static axis limits using pre-calculated values
        try:
            if axis_limits:
                # Set static axis limits - anchored and fixed
                self.setXRange(axis_limits['x_min'], axis_limits['x_max'], padding=0)
                self.setYRange(axis_limits['y_min'], axis_limits['y_max'], padding=0)

                # Disable auto-ranging to keep it static and anchored
                self.getViewBox().setAutoVisible(x=False, y=False)
                self.getViewBox().enableAutoRange(enable=False)
                self.getViewBox().setMouseEnabled(x=False, y=False)  # Disable mouse interactions
                self.getViewBox().setMenuEnabled(False)  # Disable context menu

        except Exception as e:
            print(f"Error setting static axis limits: {e}")

        # Reposition title using pre-calculated position
        try:
            if hasattr(self, 'title_label') and title_position:
                self.title_label.setPos(title_position['x'], title_position['y'])

        except Exception as e:
            print(f"Error repositioning title: {e}")


class DensityChart(pg.PlotWidget):
    """Density Graph chart widget with projected highs/lows and connecting lines."""

    def __init__(self):
        super().__init__()
        self.setBackground('#1e1e1e')
        self.showGrid(x=False, y=False)  # Disable grid

        # Remove X and Y axes
        self.hideAxis('left')
        self.hideAxis('bottom')

        # Options display settings
        self.price_type = 'ask'  # Default to ask prices
        self.selected_expiry = None  # Will be set when data is loaded

        # Profit/loss line calculations
        self.global_average_iv = 0.0  # Global average IV for all profit/loss lines
        self._averages_ready = False  # Flag to indicate if averages have been computed
        self._first_load_complete = False  # Flag to track first successful data load
        self._initialization_attempts = 0  # Track initialization attempts
        self._max_init_attempts = 3  # Maximum attempts before giving up

        # Simple crosshair implementation
        self.crosshair_enabled = False
        self.mouse_proxy = None

    def enable_crosshair(self):
        """Enable crosshair functionality with direct mouse tracking."""
        if not self.crosshair_enabled:
            # Create crosshair lines with bright colors and high Z-value to be on top
            self.crosshair_v = pg.InfiniteLine(angle=90, movable=False,
                                             pen=pg.mkPen(color='red', width=5))
            self.crosshair_h = pg.InfiniteLine(angle=0, movable=False,
                                             pen=pg.mkPen(color='red', width=5))

            # Set high Z-value to ensure crosshair is on top
            self.crosshair_v.setZValue(1000)
            self.crosshair_h.setZValue(1000)

            # Position crosshair at center initially to make it visible
            self.crosshair_v.setPos(0)
            self.crosshair_h.setPos(600)  # Approximate center price

            # Add to plot with high priority
            self.addItem(self.crosshair_v, ignoreBounds=True)
            self.addItem(self.crosshair_h, ignoreBounds=True)

            # Create crosshair label with bright background and high Z-value
            self.crosshair_label = pg.TextItem(text="$600.00", color='yellow',
                                             fill=pg.mkBrush(0, 0, 0, 200),
                                             border=pg.mkPen(color='red', width=2))
            self.crosshair_label.setZValue(1001)
            self.crosshair_label.setPos(0.2, 602)  # Position near crosshair
            self.addItem(self.crosshair_label, ignoreBounds=True)

            # Connect to scene mouse move signal
            self.scene().sigMouseMoved.connect(self.on_mouse_moved)

            # Also enable mouse tracking on the widget
            self.setMouseTracking(True)

            # Enable mouse events on the plot item
            self.plotItem.vb.setMouseEnabled(x=True, y=True)

            self.crosshair_enabled = True
            # Crosshair enabled

    def on_mouse_moved(self, pos):
        """Handle mouse movement for crosshair display."""
        if not self.crosshair_enabled:
            return

        try:
            # Check if mouse is within the plot area
            if self.sceneBoundingRect().contains(pos):
                # Convert scene coordinates to view coordinates
                mouse_point = self.plotItem.vb.mapSceneToView(pos)
                x_pos = mouse_point.x()
                y_pos = mouse_point.y()

                # Update crosshair position
                self.crosshair_v.setPos(x_pos)
                self.crosshair_h.setPos(y_pos)

                # Update crosshair label with Y-axis value (price)
                self.crosshair_label.setText(f"${y_pos:.2f}")
                self.crosshair_label.setPos(x_pos + 0.2, y_pos + 2)  # Offset for visibility

            else:
                # Hide crosshair when mouse is outside
                self.crosshair_v.setPos(-10000)
                self.crosshair_h.setPos(-10000)
                self.crosshair_label.setText("")

        except Exception as e:
            print(f"Crosshair error: {e}")
            # Hide crosshair on any error
            if hasattr(self, 'crosshair_v') and self.crosshair_v:
                self.crosshair_v.setPos(-10000)
            if hasattr(self, 'crosshair_h') and self.crosshair_h:
                self.crosshair_h.setPos(-10000)
            if hasattr(self, 'crosshair_label') and self.crosshair_label:
                self.crosshair_label.setText("")

    def set_price_type(self, price_type):
        """Set the price type to display (bid or ask)."""
        self.price_type = price_type

    def set_selected_expiry(self, expiry):
        """Set the selected expiration date."""
        self.selected_expiry = expiry

    def calculate_arrow_sizes(self, projected_highs, projected_lows):
        """Calculate dynamic arrow sizes based on projected high/low range.

        Returns:
            tuple: (arrow_y_size, arrow_x_size) where:
                - arrow_y_size is 3% of the height range (highest high to lowest low)
                - arrow_x_size is 0.5% of the visual box range
        """
        try:
            if not projected_highs or not projected_lows:
                return 1.5, 0.05  # Fallback to fixed sizes

            # Calculate the full range from highest projected high to lowest projected low
            highest_high = max(projected_highs)
            lowest_low = min(projected_lows)
            total_range = highest_high - lowest_low

            # Arrow height: 3% of the total price range
            arrow_y_size = total_range * 0.03

            # Arrow width: 0.5% of visual box range (DensityChart: -5x to 5x = 10 units)
            arrow_x_size = 10 * 0.005  # 0.5% of 10 = 0.05

            return arrow_y_size, arrow_x_size

        except Exception as e:
            print(f"Error calculating arrow sizes: {e}")
            import traceback
            traceback.print_exc()
            return 1.5, 0.05  # Fallback to fixed sizes

    def create_continuous_spline(self, x_points, y_points, num_output_points=500, smoothness=0.5):
        """
        Create a continuous spline through multiple points with professional-grade rendering.
        Same method as used in density_graph.py for HL lines.

        Args:
            x_points: Array of x coordinates
            y_points: Array of y coordinates
            num_output_points: Number of points to generate for the output curve
            smoothness: Controls the smoothness of the curve (0.0 to 1.0)

        Returns:
            tuple: (x_out, y_out) arrays for the smooth curve
        """
        if len(x_points) < 2 or len(y_points) < 2:
            return np.array([]), np.array([])

        if len(x_points) != len(y_points):
            return np.array([]), np.array([])

        try:
            # Sort points by x value to ensure proper ordering
            sorted_indices = np.argsort(x_points)
            x_sorted = np.array(x_points)[sorted_indices]
            y_sorted = np.array(y_points)[sorted_indices]

            # Add intermediate points to simulate IV at micro steps between strikes
            # This creates a more continuous, natural flow like professional systems
            x_enhanced = []
            y_enhanced = []

            # For each pair of consecutive points, add micro-step points in between
            for i in range(len(x_sorted) - 1):
                # Add the current point
                x_enhanced.append(x_sorted[i])
                y_enhanced.append(y_sorted[i])

                # Calculate micro steps between this point and the next
                x_start = x_sorted[i]
                x_end = x_sorted[i + 1]
                y_start = y_sorted[i]
                y_end = y_sorted[i + 1]

                # Add micro steps (every 0.05 increment as suggested)
                x_step = 0.05
                x_current = x_start + x_step

                while x_current < x_end:
                    # Calculate y value using local cubic interpolation
                    # This simulates how IV would behave between strikes
                    t = (x_current - x_start) / (x_end - x_start)

                    # Use cubic Hermite interpolation for natural IV simulation
                    # h00, h10, h01, h11 are the Hermite basis functions
                    h00 = 2*t**3 - 3*t**2 + 1
                    h10 = t**3 - 2*t**2 + t
                    h01 = -2*t**3 + 3*t**2
                    h11 = t**3 - t**2

                    # Estimate tangents for natural curve shape
                    # Use finite differences for tangent estimation
                    m0 = 0
                    m1 = 0

                    # If we have points before and after, use them for better tangent estimation
                    if i > 0:
                        m0 = (y_end - y_sorted[i-1]) / (x_end - x_sorted[i-1]) * (x_end - x_start) * 0.5
                    else:
                        m0 = (y_end - y_start) / (x_end - x_start) * (x_end - x_start) * 0.5

                    if i < len(x_sorted) - 2:
                        m1 = (y_sorted[i+2] - y_start) / (x_sorted[i+2] - x_start) * (x_end - x_start) * 0.5
                    else:
                        m1 = (y_end - y_start) / (x_end - x_start) * (x_end - x_start) * 0.5

                    # Calculate interpolated y value
                    y_interp = h00 * y_start + h10 * m0 + h01 * y_end + h11 * m1

                    # Add the micro step point
                    x_enhanced.append(x_current)
                    y_enhanced.append(y_interp)

                    # Move to next micro step
                    x_current += x_step

            # Add the last point
            x_enhanced.append(x_sorted[-1])
            y_enhanced.append(y_sorted[-1])

            # Convert to numpy arrays
            x_enhanced = np.array(x_enhanced)
            y_enhanced = np.array(y_enhanced)

            # For B-spline, we need at least k+1 points for a degree k spline
            # With our enhanced points, we should always have enough points now
            if len(x_enhanced) >= 4:
                # Use B-spline for professional-grade smoothness
                # FIXED: Use s=0 to ensure exact interpolation through control points
                # This fixes the issue where curves don't pass through exact projected values

                # Create B-spline representation with cubic splines (k=3)
                # s=0 ensures the curve passes exactly through all control points
                tck = splrep(x_enhanced, y_enhanced, k=3, s=0)

                # Generate output points with sub-pixel resolution
                # Using more points creates smoother rendering with sub-pixel precision
                x_out = np.linspace(x_enhanced[0], x_enhanced[-1], num_output_points)
                y_out = splev(x_out, tck)

                # FIXED: Ensure exact passage through ALL control points
                # Force exact values at original control point x-coordinates
                if len(x_out) > 0 and len(y_out) > 0:
                    y_out[0] = y_sorted[0]   # Exact start point
                    y_out[-1] = y_sorted[-1] # Exact end point

                    # Also ensure exact passage through intermediate control points
                    for i, (orig_x, orig_y) in enumerate(zip(x_sorted, y_sorted)):
                        # Find the closest x position in the output array
                        closest_idx = np.argmin(np.abs(x_out - orig_x))
                        y_out[closest_idx] = orig_y  # Force exact y value

            else:
                # Use cubic spline for fewer points with exact interpolation
                cs = CubicSpline(x_enhanced, y_enhanced, bc_type='natural')
                x_out = np.linspace(x_enhanced[0], x_enhanced[-1], num_output_points)
                y_out = cs(x_out)

                # FIXED: Ensure exact passage through ALL control points for cubic spline too
                if len(x_out) > 0 and len(y_out) > 0:
                    y_out[0] = y_sorted[0]   # Exact start point
                    y_out[-1] = y_sorted[-1] # Exact end point

                    # Also ensure exact passage through intermediate control points
                    for i, (orig_x, orig_y) in enumerate(zip(x_sorted, y_sorted)):
                        # Find the closest x position in the output array
                        closest_idx = np.argmin(np.abs(x_out - orig_x))
                        y_out[closest_idx] = orig_y  # Force exact y value

            return x_out, y_out

        except Exception as e:
            print(f"Failed to create continuous spline: {str(e)}")
            return np.array([]), np.array([])

    def update_statistics_display(self, projected_highs, projected_lows, parent_tab=None):
        """Update the statistics display with volatility and projected statistics."""
        try:
            if not parent_tab:
                return

            # Clear existing combined statistics
            if hasattr(parent_tab, 'combined_statistics_layout'):
                # Clear all combined statistics
                while parent_tab.combined_statistics_layout.count():
                    child = parent_tab.combined_statistics_layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()

            # Add Bias Statistics section first (always shown regardless of mode)
            self.update_bias_statistics(parent_tab)

            if not projected_highs or not projected_lows:
                return

            # Add Volatility Statistics section title and headers to combined layout
            if hasattr(parent_tab, 'combined_statistics_layout'):
                # Add volatility statistics title
                volatility_stats_title = QLabel("Volatility Statistics")
                volatility_stats_title.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
                volatility_stats_title.setStyleSheet("color: #ffffff; margin-bottom: 5px; padding: 2px;")
                parent_tab.combined_statistics_layout.addWidget(volatility_stats_title)

                # Add volatility statistics headers
                volatility_headers_widget = QWidget()
                volatility_headers_layout = QHBoxLayout()
                volatility_headers_layout.setContentsMargins(0, 0, 0, 0)
                volatility_headers_layout.setSpacing(5)

                # Empty space for checkbox column
                checkbox_spacer = QLabel("")
                checkbox_spacer.setFixedWidth(20)
                volatility_headers_layout.addWidget(checkbox_spacer, 0)

                volatility_price_header = QLabel("Volatility Prices")
                volatility_price_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
                volatility_price_header.setStyleSheet("color: #ffffff; text-align: left;")
                volatility_headers_layout.addWidget(volatility_price_header, 2)

                volatility_count_header = QLabel("Count")
                volatility_count_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
                volatility_count_header.setStyleSheet("color: #ffffff; text-align: center;")
                volatility_count_header.setAlignment(Qt.AlignmentFlag.AlignCenter)
                volatility_headers_layout.addWidget(volatility_count_header, 1)

                volatility_winrate_header = QLabel("Winrate")
                volatility_winrate_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
                volatility_winrate_header.setStyleSheet("color: #ffffff; text-align: center;")
                volatility_winrate_header.setAlignment(Qt.AlignmentFlag.AlignCenter)
                volatility_headers_layout.addWidget(volatility_winrate_header, 1)

                volatility_headers_widget.setLayout(volatility_headers_layout)
                parent_tab.combined_statistics_layout.addWidget(volatility_headers_widget)

            # Calculate and add volatility statistics data
            self.update_volatility_statistics(projected_highs, projected_lows, parent_tab)

            # Add projected statistics section title and headers to combined layout
            if hasattr(parent_tab, 'combined_statistics_layout'):
                # Add projected statistics title
                projected_stats_title = QLabel("Projected Statistics")
                projected_stats_title.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
                projected_stats_title.setStyleSheet("color: #ffffff; padding: 2px;")
                parent_tab.combined_statistics_layout.addWidget(projected_stats_title)

                # Add projected statistics headers
                projected_headers_widget = QWidget()
                projected_headers_layout = QHBoxLayout()
                projected_headers_layout.setContentsMargins(0, 0, 0, 0)
                projected_headers_layout.setSpacing(5)

                # Empty space for checkbox column
                checkbox_spacer2 = QLabel("")
                checkbox_spacer2.setFixedWidth(20)
                projected_headers_layout.addWidget(checkbox_spacer2, 0)

                price_header = QLabel("Projected Prices")
                price_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
                price_header.setStyleSheet("color: #ffffff; text-align: left;")
                projected_headers_layout.addWidget(price_header, 2)

                count_header = QLabel("Count")
                count_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
                count_header.setStyleSheet("color: #ffffff; text-align: center;")
                count_header.setAlignment(Qt.AlignmentFlag.AlignCenter)
                projected_headers_layout.addWidget(count_header, 1)

                winrate_header = QLabel("Winrate")
                winrate_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
                winrate_header.setStyleSheet("color: #ffffff; text-align: center;")
                winrate_header.setAlignment(Qt.AlignmentFlag.AlignCenter)
                projected_headers_layout.addWidget(winrate_header, 1)

                projected_headers_widget.setLayout(projected_headers_layout)
                parent_tab.combined_statistics_layout.addWidget(projected_headers_widget)

            # Then update projected statistics
            # Sort projected highs from high to low, then projected lows from high to low
            sorted_highs = sorted(projected_highs, reverse=True)  # Highest to lowest
            sorted_lows = sorted(projected_lows, reverse=True)   # Highest to lowest

            total_count = len(projected_highs) + len(projected_lows)

            # Add projected highs first (high to low order)
            for high_price in sorted_highs:
                # Create unified row widget with horizontal layout (1/2, 1/4, 1/4)
                row_widget = QWidget()
                row_layout = QHBoxLayout()
                row_layout.setContentsMargins(0, 0, 0, 0)
                row_layout.setSpacing(5)

                # Left section (1/2) - Projected High Price
                price_label = QLabel(f"Projected High Price $ at or below ${high_price:.2f}")
                price_label.setFont(QFont("Segoe UI", 7))
                price_label.setStyleSheet("color: #ffffff; padding: 1px;")

                # Middle section (1/4) - Count
                # For highs: count ALL values (both highs and lows) that are at or below this high price
                all_values_at_or_below = 0
                all_values_at_or_below += len([high for high in projected_highs if high <= high_price])
                all_values_at_or_below += len([low for low in projected_lows if low <= high_price])
                count = all_values_at_or_below

                count_label = QLabel(f"{count}/{total_count}")
                count_label.setFont(QFont("Segoe UI", 7))
                count_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

                # Right section (1/4) - Winrate
                winrate = (count / total_count) * 100
                winrate_label = QLabel(f"{winrate:.1f}%")
                winrate_label.setFont(QFont("Segoe UI", 7))
                winrate_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                winrate_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

                # Checkbox (same style as market_odds.py chart settings)
                checkbox = QCheckBox()
                checkbox.setChecked(False)  # Off by default
                self._style_statistics_checkbox(checkbox)

                # Connect checkbox to draw line functionality (now that count and winrate are calculated)
                checkbox.stateChanged.connect(
                    lambda state, price=high_price, is_high=True, c=count, wr=winrate:
                    self._on_projected_checkbox_changed(state, price, is_high, c, wr)
                )

                # Add widgets to layout
                row_layout.addWidget(checkbox, 0)  # No stretch, fixed size
                row_layout.addWidget(price_label, 2)  # 1/2 proportion
                row_layout.addWidget(count_label, 1)  # 1/4 proportion
                row_layout.addWidget(winrate_label, 1)  # 1/4 proportion

                row_widget.setLayout(row_layout)
                parent_tab.combined_statistics_layout.addWidget(row_widget)

            # Add projected lows second (high to low order)
            for low_price in sorted_lows:
                # Create unified row widget with horizontal layout (1/2, 1/4, 1/4)
                row_widget = QWidget()
                row_layout = QHBoxLayout()
                row_layout.setContentsMargins(0, 0, 0, 0)
                row_layout.setSpacing(5)

                # Left section (1/2) - Projected Low Price
                price_label = QLabel(f"Projected Low Price $ at or above ${low_price:.2f}")
                price_label.setFont(QFont("Segoe UI", 7))
                price_label.setStyleSheet("color: #ffffff; padding: 1px;")

                # Middle section (1/4) - Count
                # For lows: count ALL values (both highs and lows) that are at or above this low price
                all_values_at_or_above = 0
                all_values_at_or_above += len([high for high in projected_highs if high >= low_price])
                all_values_at_or_above += len([low for low in projected_lows if low >= low_price])
                count = all_values_at_or_above

                count_label = QLabel(f"{count}/{total_count}")
                count_label.setFont(QFont("Segoe UI", 7))
                count_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

                # Right section (1/4) - Winrate
                winrate = (count / total_count) * 100
                winrate_label = QLabel(f"{winrate:.1f}%")
                winrate_label.setFont(QFont("Segoe UI", 7))
                winrate_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                winrate_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

                # Checkbox (same style as market_odds.py chart settings)
                checkbox = QCheckBox()
                checkbox.setChecked(False)  # Off by default
                self._style_statistics_checkbox(checkbox)

                # Connect checkbox to draw line functionality (now that count and winrate are calculated)
                checkbox.stateChanged.connect(
                    lambda state, price=low_price, is_high=False, c=count, wr=winrate:
                    self._on_projected_checkbox_changed(state, price, is_high, c, wr)
                )

                # Add widgets to layout
                row_layout.addWidget(checkbox, 0)  # No stretch, fixed size
                row_layout.addWidget(price_label, 2)  # 1/2 proportion
                row_layout.addWidget(count_label, 1)  # 1/4 proportion
                row_layout.addWidget(winrate_label, 1)  # 1/4 proportion

                row_widget.setLayout(row_layout)
                parent_tab.combined_statistics_layout.addWidget(row_widget)

        except Exception as e:
            print(f"Error updating statistics display: {e}")

    def update_bias_statistics(self, parent_tab):
        """Update the bias statistics section based on weekday and H/L matching data."""
        try:
            # Get market data from parent tab
            market_data = getattr(parent_tab, '_market_data', None)
            if not market_data:
                return

            projected_ohlc_rows = market_data.get('projected_ohlc_table_rows', [])

            if not projected_ohlc_rows:
                return

            # Apply historical filter if parent tab is viewing historical data
            filtered_projected_ohlc_rows = projected_ohlc_rows
            if hasattr(parent_tab, 'viewing_historical') and parent_tab.viewing_historical:
                if hasattr(parent_tab, 'projected_ohlc_historical_cutoff'):
                    cutoff_idx = parent_tab.projected_ohlc_historical_cutoff
                    filtered_projected_ohlc_rows = projected_ohlc_rows[:cutoff_idx + 1]
                    print(f"BIAS UPDATE: Applying historical filter - using {len(filtered_projected_ohlc_rows)} out of {len(projected_ohlc_rows)} OHLC rows")

            # Calculate weekday bias statistics
            weekday_bias = self._calculate_weekday_bias(filtered_projected_ohlc_rows)

            # Calculate H/L matching bias statistics
            hl_bias = self._calculate_hl_matching_bias(filtered_projected_ohlc_rows, market_data)

            # Add bias statistics section title and headers to combined layout
            if hasattr(parent_tab, 'combined_statistics_layout'):
                # Add bias statistics title
                bias_stats_title = QLabel("Bias")
                bias_stats_title.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
                bias_stats_title.setStyleSheet("color: #ffffff; margin-bottom: 5px; padding: 2px;")
                parent_tab.combined_statistics_layout.addWidget(bias_stats_title)

                # Add weekday bias statistics
                if weekday_bias:
                    for label, data in weekday_bias.items():
                        if isinstance(data, dict):
                            self._add_bias_row(parent_tab, label, data["percentage"], data["count"])
                        else:
                            self._add_bias_row(parent_tab, label, data, "")

                # Add H/L matching bias statistics
                if hl_bias:
                    for label, data in hl_bias.items():
                        if isinstance(data, dict):
                            self._add_bias_row(parent_tab, label, data["percentage"], data["count"])
                        else:
                            self._add_bias_row(parent_tab, label, data, "")

        except Exception as e:
            print(f"Error updating bias statistics: {e}")
            import traceback
            traceback.print_exc()

    def _calculate_weekday_bias(self, projected_ohlc_rows):
        """Calculate weekday-based bias statistics using today's weekday to predict next day performance."""
        try:
            # Get current weekday from the last row (most recent data)
            current_weekday = None
            if projected_ohlc_rows:
                last_row = projected_ohlc_rows[-1]
                if len(last_row) > 1:
                    current_weekday = last_row[1]  # Weekday is at index 1

            if not current_weekday:
                return {}

            # CORRECTED LOGIC: Use today's weekday data to predict tomorrow's performance
            # For each historical occurrence of today's weekday, look at what happened the NEXT day
            next_day_ratios = []

            for i in range(len(projected_ohlc_rows) - 1):  # Exclude last row since it has no "next day"
                current_row = projected_ohlc_rows[i]
                next_row = projected_ohlc_rows[i + 1]

                if len(current_row) > 1 and len(next_row) > 12:
                    current_day_weekday = current_row[1]  # Weekday of current day
                    next_day_close_ratio_str = next_row[12]  # Close ratio of NEXT day

                    # If current day matches today's weekday, use next day's performance
                    if current_day_weekday == current_weekday and next_day_close_ratio_str and next_day_close_ratio_str != "":
                        try:
                            next_day_close_ratio = float(next_day_close_ratio_str)
                            next_day_ratios.append(next_day_close_ratio)
                        except (ValueError, TypeError):
                            continue

            # Calculate bias percentages for next day based on historical next-day performance
            bias_results = {}
            if next_day_ratios:
                bullish_count = len([r for r in next_day_ratios if r > 1.0])
                bearish_count = len([r for r in next_day_ratios if r < 1.0])
                total_count = len(next_day_ratios)

                if total_count > 0:
                    bullish_pct = (bullish_count / total_count) * 100
                    bearish_pct = (bearish_count / total_count) * 100

                    # Show odds for NEXT DAY based on historical next-day performance after today's weekday
                    bias_results["Odds of Next Day Bullish"] = {"percentage": bullish_pct, "count": f"{bullish_count}/{total_count}"}
                    bias_results["Odds of Next Day Bearish"] = {"percentage": bearish_pct, "count": f"{bearish_count}/{total_count}"}

                    # Log corrected weekday bias calculation
                    print(f"Weekday Bias: Today is {current_weekday}, predicting tomorrow using {total_count} historical next-day occurrences - Bullish: {bullish_pct:.1f}% ({bullish_count}/{total_count}), Bearish: {bearish_pct:.1f}% ({bearish_count}/{total_count})")

            return bias_results

        except Exception as e:
            print(f"Error calculating weekday bias: {e}")
            return {}

    def _calculate_hl_matching_bias(self, projected_ohlc_rows, market_data):
        """Calculate H/L matching bias statistics."""
        try:
            # Get the last cycle information from crosshair data
            crosshair_info_lookup = market_data.get('crosshair_info_lookup', {})

            # Find the last cycle (most recent entry with category)
            last_cycle = None
            last_index = len(projected_ohlc_rows) - 1

            # Look backwards from the end to find the last cycle
            for i in range(last_index, -1, -1):
                if str(i) in crosshair_info_lookup:
                    info = crosshair_info_lookup[str(i)]
                    if isinstance(info, dict) and 'category' in info:
                        category = info['category']
                        if category and (category.startswith('H') or category.startswith('L')):
                            last_cycle = category.split('⇨')[0] if '⇨' in category else category
                            break

            if not last_cycle:
                return {}

            # Parse the last cycle to get type and number
            if last_cycle.startswith('H'):
                cycle_type = 'H'
                cycle_number = int(last_cycle[1:]) if len(last_cycle) > 1 else 1
            elif last_cycle.startswith('L'):
                cycle_type = 'L'
                cycle_number = int(last_cycle[1:]) if len(last_cycle) > 1 else 1
            else:
                return {}

            # Collect data for H/L matching analysis
            hl_data = {}

            for i, row in enumerate(projected_ohlc_rows):
                if len(row) > 12 and str(i) in crosshair_info_lookup:
                    info = crosshair_info_lookup[str(i)]
                    if isinstance(info, dict) and 'category' in info:
                        category = info['category']
                        if category and (category.startswith('H') or category.startswith('L')):
                            clean_category = category.split('⇨')[0] if '⇨' in category else category
                            close_ratio_str = row[12]  # Close ratio is at index 12

                            if close_ratio_str and close_ratio_str != "":
                                try:
                                    close_ratio = float(close_ratio_str)

                                    if clean_category not in hl_data:
                                        hl_data[clean_category] = []
                                    hl_data[clean_category].append(close_ratio)
                                except (ValueError, TypeError):
                                    continue

            # Calculate bias for what comes AFTER the current cycle
            # Use CURRENT CYCLE data to predict what happens next
            bias_results = {}

            # Use CURRENT cycle historical data (not target data!)
            if last_cycle in hl_data:
                current_cycle_ratios = hl_data[last_cycle]

                # Determine what to predict based on current cycle
                if cycle_type == 'H':
                    # If current cycle is H, predict odds of L1 and H(number+1)
                    target_l = 'L1'
                    target_h = f'H{cycle_number + 1}'
                elif cycle_type == 'L':
                    # If current cycle is L, predict odds of H1 and L(number+1)
                    target_h = 'H1'
                    target_l = f'L{cycle_number + 1}'
                else:
                    return {}

                # Calculate bias using CURRENT CYCLE data
                if current_cycle_ratios:
                    bullish_count = len([r for r in current_cycle_ratios if r > 1.0])
                    bearish_count = len([r for r in current_cycle_ratios if r < 1.0])
                    total_count = len(current_cycle_ratios)

                    if total_count > 0:
                        bullish_pct = (bullish_count / total_count) * 100
                        bearish_pct = (bearish_count / total_count) * 100

                        # Show predictions based on current cycle data
                        if cycle_type == 'H':
                            bias_results[f"Odds of Next Day {target_l}"] = {"percentage": bullish_pct, "count": f"{bullish_count}/{total_count}"}
                            bias_results[f"Odds of Next Day {target_h}"] = {"percentage": bearish_pct, "count": f"{bearish_count}/{total_count}"}
                            targets = f"{target_l} and {target_h}"
                        elif cycle_type == 'L':
                            bias_results[f"Odds of Next Day {target_h}"] = {"percentage": bullish_pct, "count": f"{bullish_count}/{total_count}"}
                            bias_results[f"Odds of Next Day {target_l}"] = {"percentage": bearish_pct, "count": f"{bearish_count}/{total_count}"}
                            targets = f"{target_h} and {target_l}"

                        # Log category bias calculation
                        print(f"Category Bias: Using {last_cycle} data ({total_count} occurrences) to predict {targets} - Bullish: {bullish_pct:.1f}% ({bullish_count}/{total_count}), Bearish: {bearish_pct:.1f}% ({bearish_count}/{total_count})")

            return bias_results

        except Exception as e:
            print(f"Error calculating H/L matching bias: {e}")
            return {}

    def _add_bias_row(self, parent_tab, label, percentage, count=""):
        """Add a bias statistics row to the combined layout."""
        try:
            # Create row widget
            row_widget = QWidget()
            row_layout = QHBoxLayout()
            row_layout.setContentsMargins(0, 0, 0, 0)
            row_layout.setSpacing(5)

            # Label (takes most of the space)
            label_widget = QLabel(label)
            label_widget.setFont(QFont("Segoe UI", 7))
            label_widget.setStyleSheet("color: #ffffff; padding: 1px;")
            row_layout.addWidget(label_widget, 2)  # 2/4 proportion

            # Count (if provided)
            if count:
                count_widget = QLabel(count)
                count_widget.setFont(QFont("Segoe UI", 7))
                count_widget.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                count_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
                row_layout.addWidget(count_widget, 1)  # 1/4 proportion

            # Percentage (takes remaining space)
            percentage_widget = QLabel(f"{percentage:.1f}%")
            percentage_widget.setFont(QFont("Segoe UI", 7))
            percentage_widget.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
            percentage_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
            row_layout.addWidget(percentage_widget, 1)  # 1/4 proportion

            row_widget.setLayout(row_layout)
            parent_tab.combined_statistics_layout.addWidget(row_widget)

        except Exception as e:
            print(f"Error adding bias row: {e}")

    def update_bias_statistics_standalone(self):
        """Update bias statistics independently - called from main update_data method."""
        try:
            # Check if we have market data
            if not hasattr(self, '_market_data') or not self._market_data:
                return

            market_data = self._market_data
            projected_ohlc_rows = market_data.get('projected_ohlc_table_rows', [])

            if not projected_ohlc_rows:
                return

            # Apply historical filter if viewing historical data
            filtered_projected_ohlc_rows = projected_ohlc_rows
            if hasattr(self, 'viewing_historical') and self.viewing_historical:
                if hasattr(self, 'projected_ohlc_historical_cutoff'):
                    cutoff_idx = self.projected_ohlc_historical_cutoff
                    filtered_projected_ohlc_rows = projected_ohlc_rows[:cutoff_idx + 1]
                    print(f"BIAS STANDALONE: Applying historical filter - using {len(filtered_projected_ohlc_rows)} out of {len(projected_ohlc_rows)} OHLC rows")

            # Calculate weekday bias statistics
            weekday_bias = self._calculate_weekday_bias(filtered_projected_ohlc_rows)

            # Calculate H/L matching bias statistics
            hl_bias = self._calculate_hl_matching_bias(filtered_projected_ohlc_rows, market_data)

            # Find the combined statistics layout
            if hasattr(self, 'combined_statistics_layout'):
                # Clear existing bias statistics (but not all statistics)
                self._clear_bias_statistics()

                # Add bias statistics title
                bias_stats_title = QLabel("Bias")
                bias_stats_title.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
                bias_stats_title.setStyleSheet("color: #ffffff; margin-bottom: 5px; padding: 2px;")
                bias_stats_title.setObjectName("bias_title")  # For identification
                self.combined_statistics_layout.insertWidget(0, bias_stats_title)  # Insert at top

                # Add bias column headers
                self._add_bias_headers()

                # Add weekday bias statistics
                if weekday_bias:
                    for label, data in weekday_bias.items():
                        if isinstance(data, dict):
                            self._add_bias_row_standalone(label, data["percentage"], data["count"])
                        else:
                            # Fallback for old format
                            self._add_bias_row_standalone(label, data, "")

                # Add H/L matching bias statistics
                if hl_bias:
                    for label, data in hl_bias.items():
                        if isinstance(data, dict):
                            self._add_bias_row_standalone(label, data["percentage"], data["count"])
                        else:
                            # Fallback for old format
                            self._add_bias_row_standalone(label, data, "")

        except Exception as e:
            print(f"Error updating standalone bias statistics: {e}")

    def _clear_bias_statistics(self):
        """Clear existing bias statistics from the layout."""
        try:
            if hasattr(self, 'combined_statistics_layout'):
                # Remove bias-related widgets
                for i in range(self.combined_statistics_layout.count() - 1, -1, -1):
                    item = self.combined_statistics_layout.itemAt(i)
                    if item and item.widget():
                        widget = item.widget()
                        if (hasattr(widget, 'objectName') and
                            (widget.objectName().startswith('bias_') or
                             widget.objectName() == 'bias_title' or
                             widget.objectName() == 'bias_headers')):
                            self.combined_statistics_layout.removeWidget(widget)
                            widget.deleteLater()
        except Exception as e:
            print(f"Error clearing bias statistics: {e}")

    def _add_bias_headers(self):
        """Add column headers for bias statistics."""
        try:
            # Create header widget
            header_widget = QWidget()
            header_widget.setObjectName("bias_headers")  # For identification
            header_layout = QHBoxLayout()
            header_layout.setContentsMargins(0, 0, 0, 0)
            header_layout.setSpacing(5)

            # Odds header (2/4 proportion)
            odds_header = QLabel("Odds")
            odds_header.setFont(QFont("Segoe UI", 7, QFont.Weight.Bold))
            odds_header.setStyleSheet("color: #ffffff; padding: 1px;")
            header_layout.addWidget(odds_header, 2)

            # Count header (1/4 proportion)
            count_header = QLabel("Count")
            count_header.setFont(QFont("Segoe UI", 7, QFont.Weight.Bold))
            count_header.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
            count_header.setAlignment(Qt.AlignmentFlag.AlignCenter)
            header_layout.addWidget(count_header, 1)

            # Winrate header (1/4 proportion)
            winrate_header = QLabel("Winrate")
            winrate_header.setFont(QFont("Segoe UI", 7, QFont.Weight.Bold))
            winrate_header.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
            winrate_header.setAlignment(Qt.AlignmentFlag.AlignCenter)
            header_layout.addWidget(winrate_header, 1)

            header_widget.setLayout(header_layout)

            # Insert after the bias title
            insert_position = 1  # Default position after title
            for i in range(self.combined_statistics_layout.count()):
                item = self.combined_statistics_layout.itemAt(i)
                if item and item.widget() and hasattr(item.widget(), 'objectName'):
                    if item.widget().objectName() == 'bias_title':
                        insert_position = i + 1
                        break

            self.combined_statistics_layout.insertWidget(insert_position, header_widget)

        except Exception as e:
            print(f"Error adding bias headers: {e}")

    def _add_bias_row_standalone(self, label, percentage, count=""):
        """Add a bias statistics row to the combined layout (standalone version)."""
        try:
            # Create row widget
            row_widget = QWidget()
            row_widget.setObjectName(f"bias_row_{len(label)}")  # For identification
            row_layout = QHBoxLayout()
            row_layout.setContentsMargins(0, 0, 0, 0)
            row_layout.setSpacing(5)

            # Label (takes most of the space)
            label_widget = QLabel(label)
            label_widget.setFont(QFont("Segoe UI", 7))
            label_widget.setStyleSheet("color: #ffffff; padding: 1px;")
            row_layout.addWidget(label_widget, 2)  # 2/4 proportion

            # Count (if provided)
            if count:
                count_widget = QLabel(count)
                count_widget.setFont(QFont("Segoe UI", 7))
                count_widget.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                count_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
                row_layout.addWidget(count_widget, 1)  # 1/4 proportion

            # Percentage (takes remaining space)
            percentage_widget = QLabel(f"{percentage:.1f}%")
            percentage_widget.setFont(QFont("Segoe UI", 7))
            percentage_widget.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
            percentage_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
            row_layout.addWidget(percentage_widget, 1)  # 1/4 proportion

            row_widget.setLayout(row_layout)

            # Insert after the bias title (find its position)
            insert_position = 1  # Default position after title
            for i in range(self.combined_statistics_layout.count()):
                item = self.combined_statistics_layout.itemAt(i)
                if item and item.widget() and hasattr(item.widget(), 'objectName'):
                    if item.widget().objectName() == 'bias_title':
                        insert_position = i + 1
                        break

            # Find the last bias row position
            for i in range(insert_position, self.combined_statistics_layout.count()):
                item = self.combined_statistics_layout.itemAt(i)
                if item and item.widget() and hasattr(item.widget(), 'objectName'):
                    if not item.widget().objectName().startswith('bias_'):
                        insert_position = i
                        break
                else:
                    insert_position = i
                    break

            self.combined_statistics_layout.insertWidget(insert_position, row_widget)

        except Exception as e:
            print(f"Error adding standalone bias row: {e}")

    def update_volatility_statistics(self, projected_highs, projected_lows, parent_tab):
        """Update the volatility statistics section."""
        try:
            import statistics

            # Calculate statistics for highs and create list with counts
            highs_data = []
            if projected_highs:
                sorted_highs = sorted(projected_highs)
                n_highs = len(sorted_highs)

                # Calculate volatility levels for highs
                # Calculate apex as midpoint between max high and max low
                max_high_value = max(sorted_highs) if sorted_highs else None
                max_low_value = min(projected_lows) if projected_lows else None
                apex_value = None
                if max_high_value is not None and max_low_value is not None:
                    apex_value = (max_high_value + max_low_value) / 2.0

                highs_stats = {
                    'average': statistics.mean(sorted_highs),
                    'median': statistics.median(sorted_highs),
                    'max_avg': statistics.mean(sorted_highs[-max(1, n_highs // 2):]),  # Top 50%
                    'min_avg': statistics.mean(sorted_highs[:max(1, n_highs // 2)]),   # Bottom 50%
                    'apex': apex_value  # Midpoint between max high and max low
                }

                # Create list of highs data with counts for sorting
                for level_name, level_value in highs_stats.items():
                    count_at_or_below = len([high for high in projected_highs if high <= level_value])
                    winrate = (count_at_or_below / n_highs) * 100
                    highs_data.append((level_name, level_value, count_at_or_below, n_highs, winrate))

                # Sort highs by count (highest to lowest)
                highs_data.sort(key=lambda x: x[2], reverse=True)

                # Add volatility statistics for highs (ordered by count)
                for level_name, level_value, count_at_or_below, n_highs, winrate in highs_data:
                    # Create row for this volatility level
                    row_widget = QWidget()
                    row_layout = QHBoxLayout()
                    row_layout.setContentsMargins(0, 0, 0, 0)
                    row_layout.setSpacing(5)

                    # Checkbox (same style as market_odds.py chart settings)
                    checkbox = QCheckBox()
                    checkbox.setChecked(False)  # Off by default
                    self._style_statistics_checkbox(checkbox)

                    # Connect checkbox to draw line functionality
                    checkbox.stateChanged.connect(
                        lambda state, price=level_value, is_high=True, name=level_name, c=count_at_or_below, t=n_highs, wr=winrate:
                        self._on_volatility_checkbox_changed(state, price, is_high, name, c, t, wr)
                    )

                    row_layout.addWidget(checkbox, 0)  # No stretch, fixed size

                    # Description (1/2 width to match header)
                    desc_label = QLabel(f"High $ at or below {level_name.replace('_', ' ').title()}: ${level_value:.2f}")
                    desc_label.setFont(QFont("Segoe UI", 7))
                    desc_label.setStyleSheet("color: #ffffff; padding: 1px;")
                    row_layout.addWidget(desc_label, 2)  # 1/2 width

                    # Count (1/4 width to match header)
                    count_label = QLabel(f"{count_at_or_below}/{n_highs}")
                    count_label.setFont(QFont("Segoe UI", 7))
                    count_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                    count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    row_layout.addWidget(count_label, 1)  # 1/4 width

                    # Winrate (1/4 width to match header)
                    winrate_label = QLabel(f"{winrate:.1f}%")
                    winrate_label.setFont(QFont("Segoe UI", 7))
                    winrate_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                    winrate_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    row_layout.addWidget(winrate_label, 1)  # 1/4 width

                    row_widget.setLayout(row_layout)
                    parent_tab.combined_statistics_layout.addWidget(row_widget)

            # Calculate statistics for lows and create list with counts
            lows_data = []
            if projected_lows:
                sorted_lows = sorted(projected_lows)
                n_lows = len(sorted_lows)

                # Calculate volatility levels for lows
                # Calculate apex as midpoint between max high and max low (same as highs)
                max_high_value = max(projected_highs) if projected_highs else None
                max_low_value = min(sorted_lows) if sorted_lows else None
                apex_value = None
                if max_high_value is not None and max_low_value is not None:
                    apex_value = (max_high_value + max_low_value) / 2.0

                lows_stats = {
                    'average': statistics.mean(sorted_lows),
                    'median': statistics.median(sorted_lows),
                    'min_avg': statistics.mean(sorted_lows[-max(1, n_lows // 2):]),  # Top 50% (highest lows)
                    'max_avg': statistics.mean(sorted_lows[:max(1, n_lows // 2)]),   # Bottom 50% (lowest lows)
                    'apex': apex_value  # Midpoint between max high and max low
                }

                # Create list of lows data with counts for sorting
                for level_name, level_value in lows_stats.items():
                    count_at_or_above = len([low for low in projected_lows if low >= level_value])
                    winrate = (count_at_or_above / n_lows) * 100
                    lows_data.append((level_name, level_value, count_at_or_above, n_lows, winrate))

                # Sort lows by count (highest to lowest)
                lows_data.sort(key=lambda x: x[2], reverse=True)

                # Add volatility statistics for lows (ordered by count)
                for level_name, level_value, count_at_or_above, n_lows, winrate in lows_data:
                    # Create row for this volatility level
                    row_widget = QWidget()
                    row_layout = QHBoxLayout()
                    row_layout.setContentsMargins(0, 0, 0, 0)
                    row_layout.setSpacing(5)

                    # Checkbox (same style as market_odds.py chart settings)
                    checkbox = QCheckBox()
                    checkbox.setChecked(False)  # Off by default
                    self._style_statistics_checkbox(checkbox)

                    # Connect checkbox to draw line functionality
                    checkbox.stateChanged.connect(
                        lambda state, price=level_value, is_high=False, name=level_name, c=count_at_or_above, t=n_lows, wr=winrate:
                        self._on_volatility_checkbox_changed(state, price, is_high, name, c, t, wr)
                    )

                    row_layout.addWidget(checkbox, 0)  # No stretch, fixed size

                    # Description (1/2 width to match header)
                    desc_label = QLabel(f"Low $ at or above {level_name.replace('_', ' ').title()}: ${level_value:.2f}")
                    desc_label.setFont(QFont("Segoe UI", 7))
                    desc_label.setStyleSheet("color: #ffffff; padding: 1px;")
                    row_layout.addWidget(desc_label, 2)  # 1/2 width

                    # Count (1/4 width to match header)
                    count_label = QLabel(f"{count_at_or_above}/{n_lows}")
                    count_label.setFont(QFont("Segoe UI", 7))
                    count_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                    count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    row_layout.addWidget(count_label, 1)  # 1/4 width

                    # Winrate (1/4 width to match header)
                    winrate_label = QLabel(f"{winrate:.1f}%")
                    winrate_label.setFont(QFont("Segoe UI", 7))
                    winrate_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                    winrate_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    row_layout.addWidget(winrate_label, 1)  # 1/4 width

                    row_widget.setLayout(row_layout)
                    parent_tab.combined_statistics_layout.addWidget(row_widget)

        except Exception as e:
            print(f"Error updating volatility statistics: {e}")

    def _style_statistics_checkbox(self, checkbox):
        """Apply native PyQt6 styling to checkbox - smaller version for statistics."""
        # Set smaller size and remove borders
        checkbox.setStyleSheet("""
            QCheckBox {
                border: none;
                spacing: 2px;
            }
            QCheckBox::indicator {
                width: 12px;
                height: 12px;
                border: 1px solid #555555;
                border-radius: 2px;
                background-color: #2d2d2d;
            }
            QCheckBox::indicator:checked {
                background-color: #808080;
                border: 1px solid #606060;
            }
        """)

        # Set smaller font for compact appearance
        font = QFont("Consolas", 7)  # Even smaller font
        if not font.exactMatch():
            font = QFont("Courier New", 7)
        if not font.exactMatch():
            font = QFont("monospace", 7)
        checkbox.setFont(font)

        # Set smaller minimum height for compact appearance
        checkbox.setMinimumHeight(12)
        checkbox.setMaximumHeight(12)

        # Enable focus policy for keyboard navigation
        checkbox.setFocusPolicy(Qt.FocusPolicy.TabFocus)

    def _on_projected_checkbox_changed(self, state, price, is_high, count, winrate):
        """Handle projected statistics checkbox state changes."""
        try:
            # Find all chart widgets to draw lines on
            chart_widgets = self._find_chart_widgets()
            if not chart_widgets:
                return

            if state == Qt.CheckState.Checked.value:
                # Draw line when checked
                color = 'green' if is_high else 'red'
                percentage = winrate  # Use the winrate directly
                label = f"{percentage:.1f}%: ${price:.2f}"

                # Store references for removal later
                if not hasattr(self, '_projected_lines'):
                    self._projected_lines = {}

                lines_and_texts = []

                # Add infinite horizontal line to all chart widgets
                for chart_widget in chart_widgets:
                    # Add infinite horizontal line at the price level (no built-in label)
                    line = pg.InfiniteLine(
                        pos=price,
                        angle=0,  # Horizontal line
                        pen=pg.mkPen(color=color, width=2, style=pg.QtCore.Qt.PenStyle.DashLine)
                    )
                    chart_widget.addItem(line)

                    # Add text label - right-aligned
                    if is_high:
                        # Label above the line for highs
                        text_item = pg.TextItem(label, anchor=(1, 1), color=color)
                        # Get chart view range to position at right edge
                        view_range = chart_widget.viewRange()
                        x_max = view_range[0][1]  # Right edge of view
                        text_item.setPos(x_max, price)  # Position at right edge, above line
                    else:
                        # Label below the line for lows
                        text_item = pg.TextItem(label, anchor=(1, 0), color=color)
                        # Get chart view range to position at right edge
                        view_range = chart_widget.viewRange()
                        x_max = view_range[0][1]  # Right edge of view
                        text_item.setPos(x_max, price)  # Position at right edge, below line

                    chart_widget.addItem(text_item)
                    lines_and_texts.append((line, text_item, chart_widget))

                self._projected_lines[price] = lines_and_texts

            else:
                # Remove line when unchecked
                if hasattr(self, '_projected_lines') and price in self._projected_lines:
                    lines_and_texts = self._projected_lines[price]
                    for line, text_item, chart_widget in lines_and_texts:
                        chart_widget.removeItem(line)
                        chart_widget.removeItem(text_item)
                    del self._projected_lines[price]

        except Exception as e:
            print(f"Error handling projected checkbox change: {e}")

    def _on_volatility_checkbox_changed(self, state, price, is_high, name, count, total, winrate):
        """Handle volatility statistics checkbox state changes."""
        try:
            # Find all chart widgets to draw lines on
            chart_widgets = self._find_chart_widgets()
            if not chart_widgets:
                return

            if state == Qt.CheckState.Checked.value:
                # Draw line when checked
                color = 'green' if is_high else 'red'
                label = f"{winrate:.1f}%: ${price:.2f}"

                # Store references for removal later
                if not hasattr(self, '_volatility_lines'):
                    self._volatility_lines = {}

                lines_and_texts = []

                # Add infinite horizontal line to all chart widgets
                for chart_widget in chart_widgets:
                    # Add infinite horizontal line at the price level (no built-in label)
                    line = pg.InfiniteLine(
                        pos=price,
                        angle=0,  # Horizontal line
                        pen=pg.mkPen(color=color, width=2, style=pg.QtCore.Qt.PenStyle.DashLine)
                    )
                    chart_widget.addItem(line)

                    # Add text label - right-aligned
                    if is_high:
                        # Label above the line for highs
                        text_item = pg.TextItem(label, anchor=(1, 1), color=color)
                        # Get chart view range to position at right edge
                        view_range = chart_widget.viewRange()
                        x_max = view_range[0][1]  # Right edge of view
                        text_item.setPos(x_max, price)  # Position at right edge, above line
                    else:
                        # Label below the line for lows
                        text_item = pg.TextItem(label, anchor=(1, 0), color=color)
                        # Get chart view range to position at right edge
                        view_range = chart_widget.viewRange()
                        x_max = view_range[0][1]  # Right edge of view
                        text_item.setPos(x_max, price)  # Position at right edge, below line

                    chart_widget.addItem(text_item)
                    lines_and_texts.append((line, text_item, chart_widget))

                self._volatility_lines[f"{name}_{price}"] = lines_and_texts

            else:
                # Remove line when unchecked
                if hasattr(self, '_volatility_lines') and f"{name}_{price}" in self._volatility_lines:
                    lines_and_texts = self._volatility_lines[f"{name}_{price}"]
                    for line, text_item, chart_widget in lines_and_texts:
                        chart_widget.removeItem(line)
                        chart_widget.removeItem(text_item)
                    del self._volatility_lines[f"{name}_{price}"]

        except Exception as e:
            print(f"Error handling volatility checkbox change: {e}")

    def _find_chart_widgets(self):
        """Find all chart widgets to draw lines on (volatility, density, and FWL odds)."""
        try:
            # Navigate up the widget hierarchy to find the tab with chart widgets
            parent = self.parent()
            while parent:
                if hasattr(parent, 'volatility_chart') and hasattr(parent, 'density_chart') and hasattr(parent, 'fwl_odds_chart'):
                    return [parent.volatility_chart, parent.density_chart, parent.fwl_odds_chart]
                parent = parent.parent()
            return []
        except Exception as e:
            print(f"Error finding chart widgets: {e}")
            return []

    def update_data(self, filtered_high_data, filtered_low_data, market_data=None):
        """Update density chart with filtered data and market data."""
        # Clear existing plot items
        self.clear()

        # Enable crosshair after clearing
        self.enable_crosshair()

        # Store market data for use in profit/loss calculations
        self.current_market_data = market_data

        # Check if we have basic market data requirements
        if not market_data:
            return

        ticker = market_data.get("ticker", "")
        if not ticker:
            return

        # Get the data service from the parent tab to perform calculations
        parent_tab = self.parent()
        while parent_tab and not hasattr(parent_tab, 'data_service'):
            parent_tab = parent_tab.parent()

        if not parent_tab or not hasattr(parent_tab, 'data_service') or not parent_tab.data_service:
            print("No data service available for density calculations")
            return

        # Use backend service to calculate all data
        calc_data = parent_tab.data_service.calculate_density_data(
            filtered_high_data, filtered_low_data, market_data,
            self.price_type, getattr(self, 'selected_expiry', None)
        )

        if not calc_data:
            print("Failed to calculate density data")
            return

        # Extract calculated values
        title_info = calc_data.get('title_info', {})
        current_close = calc_data.get('current_close')
        projected_highs = calc_data.get('projected_highs', [])
        projected_lows = calc_data.get('projected_lows', [])

        # Debug: Check what current_close the backend calculated
        print(f"DENSITY CHART DEBUG: Backend calculated current_close = ${current_close:.2f}" if current_close else "DENSITY CHART DEBUG: Backend returned None for current_close")
        lowest_low = calc_data.get('lowest_low')
        highest_high = calc_data.get('highest_high')
        highs_stats = calc_data.get('highs_stats', {})
        lows_stats = calc_data.get('lows_stats', {})
        options_data = calc_data.get('options_data')
        filtered_strikes = calc_data.get('filtered_strikes', [])
        options_averages = calc_data.get('options_averages', {})
        strike_options_data = calc_data.get('strike_options_data', {})
        complex_divisor = calc_data.get('complex_divisor', 13.25)
        axis_limits = calc_data.get('axis_limits', {})
        title_position = calc_data.get('title_position', {})

        # Store calculated values for use in other methods
        self.cached_divisor = complex_divisor
        self.cached_divisor_price_type = self.price_type

        # Log complex divisor calculation
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Complex divisor calculated: {self.cached_divisor:.6f} using {self.price_type} prices")

        # Add title to density chart using calculated values
        try:
            if title_info:
                title_text = title_info.get('title_text', '')
                title_label = pg.TextItem(title_text, color='white', anchor=(0, 0))  # Left-bottom anchor
                title_label.setFont(QFont("Segoe UI", 8))

                # Position at -5.25x, will be repositioned later
                title_label.setPos(-5.25, 0)  # Will be repositioned after Y-axis limits are calculated
                self.addItem(title_label)

                # Store title label reference to reposition it later after Y-axis limits are calculated
                self.title_label = title_label

        except Exception as e:
            print(f"Error adding title to density chart: {e}")

        # If no projected data is available, handle fallback case
        if not projected_highs and not projected_lows:
            # Create minimal projected data from market data if available
            if current_close is not None:
                # Create a minimal range around current price for options display
                price_range = current_close * 0.1  # 10% range
                fallback_lowest_low = current_close - price_range
                fallback_highest_high = current_close + price_range

                # Process options data with fallback range
                self.add_strike_prices(fallback_lowest_low, fallback_highest_high, market_data, current_close)
                return
            else:
                return

        # All projected data extraction is now done by the backend service
        # The calculated values are already extracted above

        # Add strike prices using calculated values
        try:
            if lowest_low is not None and highest_high is not None:
                # Add strike prices at -5x position per $1 increment with options data
                self.add_strike_prices(lowest_low, highest_high, market_data, current_close)
        except Exception as e:
            print(f"Error adding strike prices: {e}")

        # Add vertical lines at -1x and 0x using calculated values
        try:
            if lowest_low is not None and highest_high is not None:
                # Add vertical line at -1x
                self.plot(
                    [-1, -1], [lowest_low, highest_high],
                    pen=pg.mkPen(color='white', width=1),
                    name='Vertical Line -1x'
                )

                # Add vertical line at 0x
                self.plot(
                    [0, 0], [lowest_low, highest_high],
                    pen=pg.mkPen(color='white', width=1),
                    name='Vertical Line 0x'
                )
        except Exception as e:
            print(f"Error adding vertical lines: {e}")

        # Add connecting lines (smooth B-spline lines from -2x to 2.5x)
        try:
            if current_close is not None:
                # Create smooth B-spline lines from close price at -2x through projected low/high pairs to close price at 2.5x
                # Assuming same index correspondence between lows and highs
                min_length = min(len(projected_lows), len(projected_highs))
                for i in range(min_length):
                    # Control points: close at -2x through low at -1x, high at 0x, to close at 2.5x
                    x_points = [-2, -1, 0, 2.5]
                    y_points = [current_close, projected_lows[i], projected_highs[i], current_close]

                    # Create smooth B-spline curve using the same method as density_graph.py
                    x_smooth, y_smooth = self.create_continuous_spline(
                        x_points, y_points,
                        num_output_points=100,  # Smooth curve with 100 points
                        smoothness=0.5  # Medium smoothness
                    )

                    if len(x_smooth) > 0 and len(y_smooth) > 0:
                        # Plot the smooth curve
                        self.plot(
                            x_smooth, y_smooth,
                            pen=pg.mkPen(color='white', width=1),
                            name=f'Smooth HL Line {i}: {projected_lows[i]:.2f} to {projected_highs[i]:.2f}'
                        )
        except Exception as e:
            print(f"Error adding smooth connecting lines: {e}")

        # All statistical calculations are now done by the backend service
        # The calculated values are already extracted above

        # Add statistical lines for projected highs
        try:
            if highs_stats:
                # MaxAvg for projected high as light green dashed line from -5.5x to 5x
                if 'max_avg' in highs_stats:
                    self.plot(
                        [-5.5, 5], [highs_stats['max_avg'], highs_stats['max_avg']],
                        pen=pg.mkPen(color='lightgreen', width=1, style=pg.QtCore.Qt.PenStyle.DashLine),
                        name=f'MaxAvg High: {highs_stats["max_avg"]:.2f}'
                    )

                    # Add label for MaxAvg High - left aligned above the line
                    max_avg_high_label = pg.TextItem(f'25%: ${highs_stats["max_avg"]:.2f}', color='white', anchor=(0, 1))
                    max_avg_high_label.setPos(-5.5, highs_stats['max_avg'])  # Left aligned, above the line
                    self.addItem(max_avg_high_label)

                # Median for projected high as dark green dashed line from -5.5x to 5x
                if 'median' in highs_stats:
                    self.plot(
                        [-5.5, 5], [highs_stats['median'], highs_stats['median']],
                        pen=pg.mkPen(color='darkgreen', width=1, style=pg.QtCore.Qt.PenStyle.DashLine),
                        name=f'Median High: {highs_stats["median"]:.2f}'
                    )

                    # Add label for Median High - left aligned above the line
                    median_high_label = pg.TextItem(f'50%: ${highs_stats["median"]:.2f}', color='white', anchor=(0, 1))
                    median_high_label.setPos(-5.5, highs_stats['median'])  # Left aligned, above the line
                    self.addItem(median_high_label)
        except Exception as e:
            print(f"Error adding statistical lines for highs: {e}")

        # Add statistical lines for projected lows
        try:
            if lows_stats:
                # MaxAvg for projected low as light red dashed line from -5.5x to 5x
                if 'max_avg' in lows_stats:
                    self.plot(
                        [-5.5, 5], [lows_stats['max_avg'], lows_stats['max_avg']],
                        pen=pg.mkPen(color='lightcoral', width=1, style=pg.QtCore.Qt.PenStyle.DashLine),
                        name=f'MaxAvg Low: {lows_stats["max_avg"]:.2f}'
                    )

                    # Add label for MaxAvg Low - left aligned below the line
                    max_avg_low_label = pg.TextItem(f'25%: ${lows_stats["max_avg"]:.2f}', color='white', anchor=(0, 0))
                    max_avg_low_label.setPos(-5.5, lows_stats['max_avg'])  # Left aligned, below the line
                    self.addItem(max_avg_low_label)

                # Median for projected low as dark red dashed line from -5.5x to 5x
                if 'median' in lows_stats:
                    self.plot(
                        [-5.5, 5], [lows_stats['median'], lows_stats['median']],
                        pen=pg.mkPen(color='darkred', width=1, style=pg.QtCore.Qt.PenStyle.DashLine),
                        name=f'Median Low: {lows_stats["median"]:.2f}'
                    )

                    # Add label for Median Low - left aligned below the line
                    median_low_label = pg.TextItem(f'50%: ${lows_stats["median"]:.2f}', color='white', anchor=(0, 0))
                    median_low_label.setPos(-5.5, lows_stats['median'])  # Left aligned, below the line
                    self.addItem(median_low_label)
        except Exception as e:
            print(f"Error adding statistical lines for lows: {e}")

        # Add arrow lines at 1.75x using calculated values
        try:
            if current_close is not None and highest_high is not None and lowest_low is not None:
                # Calculate dynamic arrow sizes based on projected high/low range
                arrow_y_size, arrow_x_size = self.calculate_arrow_sizes(projected_highs, projected_lows)

                # Green line from last close to highest projected high at 1.75x with arrow head
                self.plot(
                    [1.75, 1.75], [current_close, highest_high],
                    pen=pg.mkPen(color='green', width=2),
                    name=f'Green Arrow: {current_close:.2f} to {highest_high:.2f}'
                )

                # Add arrow head pointing at highest projected high (pointing up)
                self.plot(
                    [1.75, 1.75 - arrow_x_size, 1.75 + arrow_x_size, 1.75],
                    [highest_high, highest_high - arrow_y_size, highest_high - arrow_y_size, highest_high],
                    pen=pg.mkPen(color='green', width=1),
                    brush=pg.mkBrush(color='green'),
                    fillLevel=0,
                    name='Green Arrow Head'
                )

                # Red line from last close to lowest projected low at 1.75x with arrow head
                self.plot(
                    [1.75, 1.75], [current_close, lowest_low],
                    pen=pg.mkPen(color='red', width=2),
                    name=f'Red Arrow: {current_close:.2f} to {lowest_low:.2f}'
                )

                # Add arrow head pointing at lowest projected low (pointing down)
                self.plot(
                    [1.75, 1.75 - arrow_x_size, 1.75 + arrow_x_size, 1.75],
                    [lowest_low, lowest_low + arrow_y_size, lowest_low + arrow_y_size, lowest_low],
                    pen=pg.mkPen(color='red', width=1),
                    brush=pg.mkBrush(color='red'),
                    fillLevel=0,
                    name='Red Arrow Head'
                )

        except Exception as e:
            print(f"Error adding arrow lines: {e}")

        # Set axis ranges using calculated values
        try:
            if axis_limits:
                self.setXRange(axis_limits['x_min'], axis_limits['x_max'], padding=0)
                self.setYRange(axis_limits['y_min'], axis_limits['y_max'], padding=0)

                # Disable auto-ranging to keep it static and anchored
                self.getViewBox().setAutoVisible(x=False, y=False)
                self.getViewBox().enableAutoRange(enable=False)
                self.getViewBox().setMouseEnabled(x=False, y=False)  # Disable mouse interactions
                self.getViewBox().setMenuEnabled(False)  # Disable context menu

                # Reposition title using calculated position
                if hasattr(self, 'title_label') and title_position:
                    self.title_label.setPos(title_position['x'], title_position['y'])

        except Exception as e:
            print(f"Error setting axis ranges: {e}")

        # Update statistics display
        try:
            if projected_highs and projected_lows:
                # Find parent tab to update statistics
                parent_widget = self.parent()
                while parent_widget and not hasattr(parent_widget, 'combined_statistics_layout'):
                    parent_widget = parent_widget.parent()

                if parent_widget:
                    self.update_statistics_display(projected_highs, projected_lows, parent_widget)
        except Exception as e:
            print(f"Error updating statistics display: {e}")

    def add_strike_prices(self, lowest_low, highest_high, market_data=None, current_close=None):
        """Add actual available strike prices from options data, only within the projected range."""
        try:
            # Add "Strikes" title Y using backend helper (1% above highest high)
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()
            title_y_position = highest_high + (highest_high - lowest_low) * 0.01
            if parent_tab and hasattr(parent_tab, 'data_service') and parent_tab.data_service:
                try:
                    title_y_position = parent_tab.data_service.density_calculations_service.calculate_strikes_title_y(
                        lowest_low, highest_high
                    )
                except Exception:
                    pass

            strikes_title = pg.TextItem(
                text="Strikes",
                color='white',
                anchor=(1, 0.5)  # Right-aligned
            )
            strikes_title.setPos(-4.5, title_y_position)
            self.addItem(strikes_title)

            # Add headers based on price type selection
            price_type_display = self.price_type.capitalize()

            # Call IV header at -3.5x (right-aligned)
            call_iv_title = pg.TextItem(
                text="Call IV",
                color='white',
                anchor=(1, 0.5)  # Right-aligned
            )
            call_iv_title.setPos(-3.5, title_y_position)
            self.addItem(call_iv_title)

            # Call price header at -4x (right-aligned)
            call_price_title = pg.TextItem(
                text=f"Call {price_type_display}",
                color='white',
                anchor=(1, 0.5)  # Right-aligned
            )
            call_price_title.setPos(-4, title_y_position)
            self.addItem(call_price_title)

            if self.price_type == 'ask':
                # Put ask header at 4.5x (left-aligned)
                put_price_title = pg.TextItem(
                    text="Put Ask",
                    color='white',
                    anchor=(0, 0.5)  # Left-aligned
                )
                put_price_title.setPos(4.5, title_y_position)
                self.addItem(put_price_title)

                # Put IV header at 4x (left-aligned)
                put_iv_title = pg.TextItem(
                    text="Put IV",
                    color='white',
                    anchor=(0, 0.5)  # Left-aligned
                )
                put_iv_title.setPos(4, title_y_position)
                self.addItem(put_iv_title)

            elif self.price_type == 'bid':
                # Put bid header at 4.5x (left-aligned)
                put_price_title = pg.TextItem(
                    text="Put Bid",
                    color='white',
                    anchor=(0, 0.5)  # Left-aligned
                )
                put_price_title.setPos(4.5, title_y_position)
                self.addItem(put_price_title)

                # Put IV header at 4x (left-aligned)
                put_iv_title = pg.TextItem(
                    text="Put IV",
                    color='white',
                    anchor=(0, 0.5)  # Left-aligned
                )
                put_iv_title.setPos(4, title_y_position)
                self.addItem(put_iv_title)

            # Get options data if market_data is available
            options_data = None
            if market_data:
                self._initialization_attempts += 1
                try:
                    # Try to get shared options data from parent tab first
                    parent_tab = self.parent()
                    while parent_tab and not hasattr(parent_tab, 'get_shared_options_data'):
                        parent_tab = parent_tab.parent()

                    if parent_tab and hasattr(parent_tab, 'get_shared_options_data'):
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.debug("Using shared options data from parent tab")
                        options_data = parent_tab.get_shared_options_data(market_data)
                    else:
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.debug("No parent tab found, fetching options data directly")
                        options_data = self.fetch_options_data(market_data)

                    if not options_data:
                        if self._initialization_attempts < self._max_init_attempts:
                            return  # Exit early if no options data, but allow retries
                        else:
                            return
                    else:
                        self._first_load_complete = True  # Mark successful first load
                except Exception:
                    if self._initialization_attempts < self._max_init_attempts:
                        return  # Exit early on error, but allow retries
                    else:
                        return
            else:
                return

            if options_data:
                # Use backend to filter strikes and return processed options data
                parent_tab = self.parent()
                while parent_tab and not hasattr(parent_tab, 'data_service'):
                    parent_tab = parent_tab.parent()
                filtered_strikes = []
                processed_options_data = options_data
                if parent_tab and hasattr(parent_tab, 'data_service') and parent_tab.data_service:
                    filtered_strikes, processed_options_data = (
                        parent_tab.data_service.density_calculations_service.get_filtered_strikes_and_options_data(
                            lowest_low, highest_high, options_data, getattr(self, 'selected_expiry', None)
                        )
                    )

                # First: compute and render averages (sets self.global_average_iv)
                self.add_options_averages(lowest_low, highest_high, processed_options_data, filtered_strikes)

                # Then: add strike labels, per-strike option data, and profit/loss lines
                for strike_price in filtered_strikes:
                    # Strike price at -4.5x (right-aligned)
                    strike_label = pg.TextItem(
                        text=f"{strike_price:.2f}",  # 2 decimals, no dollar sign
                        color='white',
                        anchor=(1, 0.5)  # Right-aligned
                    )
                    strike_label.setPos(-4.5, strike_price)
                    self.addItem(strike_label)

                    # Add options data for this strike
                    self.add_options_data_for_strike(strike_price, options_data)

                    # Add profit/loss lines for this strike (uses self.global_average_iv)
                    self.add_profit_loss_lines(strike_price, options_data)

                # Add current price line and label
                if current_close is not None:
                    self.add_current_price_line(current_close, lowest_low, highest_high)

        except Exception as e:
            print(f"Error adding strike prices: {e}")
            import traceback
            traceback.print_exc()

    def add_options_averages(self, lowest_low, highest_high, options_data, filtered_strikes):
        """Add average calculations for options data below the lowest projected low (visuals only)."""
        try:
            # Reset flags for a clean recalculation each update
            self.global_average_iv = 0.0
            self._averages_ready = False

            if not options_data or not filtered_strikes:
                return

            # Get backend-calculated averages and position
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()

            if not parent_tab or not hasattr(parent_tab, 'data_service') or not parent_tab.data_service:
                return

            calc = parent_tab.data_service.density_calculations_service.calculate_options_averages_for_strikes(
                lowest_low, highest_high, options_data, filtered_strikes, self.price_type
            )
            if not calc:
                return

            avg_y_position = calc.get('avg_y_position', lowest_low - (highest_high - lowest_low) * 0.01)

            # Add "Avg" label at -4.5x (right-aligned)
            avg_label = pg.TextItem(text="Avg", color='white', anchor=(1, 0.5))
            avg_label.setPos(-4.5, avg_y_position)
            self.addItem(avg_label)

            # Render call price average at -4x (right-aligned)
            call_price_avg = calc.get('call_price_avg')
            if call_price_avg is not None:
                call_price_label = pg.TextItem(text=f"{call_price_avg:.2f}", color='white', anchor=(1, 0.5))
                call_price_label.setPos(-4, avg_y_position)
                self.addItem(call_price_label)

            # Render call IV average at -3.5x (right-aligned)
            call_iv_avg = calc.get('call_iv_avg', 0.0)
            call_iv_label = pg.TextItem(text=f"{call_iv_avg:.3f}", color='white', anchor=(1, 0.5))
            call_iv_label.setPos(-3.5, avg_y_position)
            self.addItem(call_iv_label)

            # Render put IV average at 4x (left-aligned)
            put_iv_avg = calc.get('put_iv_avg', 0.0)
            put_iv_label = pg.TextItem(text=f"{put_iv_avg:.3f}", color='white', anchor=(0, 0.5))
            put_iv_label.setPos(4, avg_y_position)
            self.addItem(put_iv_label)

            # Store global average IV and readiness
            self.global_average_iv = calc.get('global_average_iv', 0.0)
            self._averages_ready = True
            self._first_load_complete = True

            # Render put price average at 4.5x (left-aligned)
            put_price_avg = calc.get('put_price_avg')
            if put_price_avg is not None:
                put_price_label = pg.TextItem(text=f"{put_price_avg:.2f}", color='white', anchor=(0, 0.5))
                put_price_label.setPos(4.5, avg_y_position)
                self.addItem(put_price_label)

        except Exception as e:
            print(f"Error adding options averages: {e}")
            import traceback
            traceback.print_exc()

    def add_current_price_line(self, current_close, lowest_low, highest_high):
        """Render current price line and label using backend-calculated label position."""
        try:
            # Add white dotted line from -5.5x to 5x at current price
            self.addItem(pg.PlotDataItem(
                x=[-5.5, 5],
                y=[current_close, current_close],
                pen=pg.mkPen(color='white', width=1, style=pg.QtCore.Qt.PenStyle.DotLine),
                connect='all'
            ))

            # Get label Y position from backend (2% above current by default)
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()
            label_y_position = current_close + (highest_high - lowest_low) * 0.02
            if parent_tab and hasattr(parent_tab, 'data_service') and parent_tab.data_service:
                try:
                    # Reuse calculate_strikes_title_y pattern but for current price offset logic if exists in future
                    # For now, local default remains; keeping hook for consistency
                    pass
                except Exception:
                    pass

            # Add label above the line (left-aligned)
            current_price_label = pg.TextItem(
                text=f"Current Price:\n${current_close:.2f}",
                color='white',
                anchor=(0, 0.5)  # Left-aligned
            )
            current_price_label.setPos(-5.5, label_y_position)
            self.addItem(current_price_label)

        except Exception as e:
            print(f"Error adding current price line: {e}")
            import traceback
            traceback.print_exc()

    def set_density_axis_ranges(self, projected_highs, projected_lows):
        """Set axis ranges for density chart anchored from -5x to 5x using backend calculations."""
        try:
            if not projected_highs or not projected_lows:
                return

            highest_high = max(projected_highs)
            lowest_low = min(projected_lows)

            # Get backend axis limits and title position
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()

            axis = {'x_min': -5.5, 'x_max': 5}
            title_pos = {'x': -5.25, 'y': highest_high + (highest_high - lowest_low) * 0.075}
            if parent_tab and hasattr(parent_tab, 'data_service') and parent_tab.data_service:
                try:
                    svc = parent_tab.data_service.density_calculations_service
                    axis = svc.calculate_axis_limits(highest_high, lowest_low)
                    title_pos = svc.calculate_title_position(highest_high, lowest_low)
                except Exception:
                    pass

            # Set static axis limits
            self.setXRange(axis['x_min'], axis['x_max'], padding=0)
            self.setYRange(axis['y_min'], axis['y_max'], padding=0)

            # Disable auto-ranging to keep it static and anchored
            self.getViewBox().setAutoVisible(x=False, y=False)
            self.getViewBox().enableAutoRange(enable=False)
            self.getViewBox().setMouseEnabled(x=False, y=False)
            self.getViewBox().setMenuEnabled(False)

            # Reposition title at backend-calculated position
            try:
                if hasattr(self, 'title_label') and self.title_label:
                    self.title_label.setPos(title_pos['x'], title_pos['y'])
            except Exception as e:
                print(f"Error repositioning title: {e}")

        except Exception as e:
            print(f"Error setting density axis ranges: {e}")

    def fetch_options_data(self, market_data, selected_expiry=None):
        """Fetch options data using the backend data service."""
        try:
            ticker = market_data.get("ticker", "")
            if not ticker:
                return None

            # Determine which expiry to use
            if selected_expiry is None:
                # Check if we have a stored selected expiry
                if self.selected_expiry:
                    selected_expiry = self.selected_expiry
                else:
                    # Let the backend service handle finding the default expiry
                    pass

            # Get the data service from the parent tab
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()

            if parent_tab and hasattr(parent_tab, 'data_service') and parent_tab.data_service:
                result = parent_tab.data_service.get_options_data(ticker, selected_expiry)
            else:
                print("No data service available for options data")
                return None

            # Store the selected expiry for future use
            if result and 'selected_expiry' in result:
                self.selected_expiry = result['selected_expiry']

            return result

        except Exception as e:
            print(f"Error fetching options data: {e}")
            import traceback
            traceback.print_exc()
            return None

    def add_options_data_for_strike(self, strike_price, options_data):
        """Render options data (ask/bid prices and IV) for a specific strike price using backend-calculated values."""
        try:
            if not options_data:
                return

            # Get backend-processed option values for this strike
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()

            if not parent_tab or not hasattr(parent_tab, 'data_service') or not parent_tab.data_service:
                return

            data = parent_tab.data_service.density_calculations_service.get_options_data_for_strike(
                strike_price, options_data, self.price_type, getattr(self, 'selected_expiry', None)
            )
            if not data:
                return

            call_price = data.get('call_price')
            call_iv = data.get('call_iv')
            put_price = data.get('put_price')
            put_iv = data.get('put_iv')

            # Display call price at -4x (right-aligned)
            if call_price is not None:
                call_price_label = pg.TextItem(text=f"{call_price:.2f}", color='white', anchor=(1, 0.5))
                call_price_label.setPos(-4, strike_price)
                self.addItem(call_price_label)

            # Display call IV at -3.5x (right-aligned)
            if call_iv is not None:
                call_iv_label = pg.TextItem(text=f"{call_iv:.3f}", color='white', anchor=(1, 0.5))
                call_iv_label.setPos(-3.5, strike_price)
                self.addItem(call_iv_label)

            # Display put price at 4.5x (left-aligned)
            if put_price is not None and self.price_type in ['ask', 'bid']:
                put_price_label = pg.TextItem(text=f"{put_price:.2f}", color='white', anchor=(0, 0.5))
                put_price_label.setPos(4.5, strike_price)
                self.addItem(put_price_label)

            # Display put IV at 4x (left-aligned)
            if put_iv is not None:
                put_iv_label = pg.TextItem(text=f"{put_iv:.3f}", color='white', anchor=(0, 0.5))
                put_iv_label.setPos(4, strike_price)
                self.addItem(put_iv_label)

            # Propagate logs for NaN->0 conversions to frontend logger
            for log_msg in data.get('logs', []):
                import logging
                logging.getLogger(__name__).info(log_msg)

        except Exception as e:
            print(f"Error adding options data for strike {strike_price}: {e}")
            import traceback
            traceback.print_exc()

    def add_profit_loss_lines(self, strike_price, options_data):
        """Render call/put P&L curves using backend-computed points and backend divisor updates."""
        try:
            if not options_data:
                return

            # Acquire data service
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()
            if not parent_tab or not hasattr(parent_tab, 'data_service') or not parent_tab.data_service:
                return

            market_data = getattr(self, 'current_market_data', {})

            # Compute curve points and divisor update in backend
            res = parent_tab.data_service.density_calculations_service.compute_profit_loss_curves(
                strike_price=strike_price,
                options_data=options_data,
                price_type=self.price_type,
                selected_expiry=getattr(self, 'selected_expiry', None),
                market_data=market_data,
                cached_divisor=getattr(self, 'cached_divisor', None),
                cached_divisor_price_type=getattr(self, 'cached_divisor_price_type', None),
            )
            if not res:
                return

            # Update cached divisor if backend indicates change
            if res.get('divisor_updated'):
                self.cached_divisor = res.get('divisor', self.cached_divisor)
                self.cached_divisor_price_type = self.price_type

            curves = res.get('curves', {})

            # Helper to plot a curve
            def _plot_curve(curve, color):
                if not curve:
                    return
                smooth = curve.get('smooth')
                base = curve.get('base')
                if smooth:
                    self.addItem(pg.PlotDataItem(x=smooth['x'], y=smooth['y'], pen=pg.mkPen(color=color, width=1), connect='all'))
                elif base:
                    self.addItem(pg.PlotDataItem(x=base['x'], y=base['y'], pen=pg.mkPen(color=color, width=1), connect='all'))

            # Render call (white) and put (white) curves as before
            _plot_curve(curves.get('call'), 'white')
            _plot_curve(curves.get('put'), 'white')

        except Exception as e:
            print(f"Error adding profit/loss lines for strike {strike_price}: {e}")
            import traceback
            traceback.print_exc()
        except Exception as e:
            print(f"Error adding profit/loss lines for strike {strike_price}: {e}")
            import traceback
            traceback.print_exc()

class FWLOddsChart(pg.PlotWidget):
    """FWL Odds chart widget with rectangle and price labels."""

    def __init__(self):
        super().__init__()
        self.setBackground('#1e1e1e')
        self.showGrid(x=False, y=False)  # Disable grid

        # Remove X and Y axes like volatility chart
        self.hideAxis('left')
        self.hideAxis('bottom')

        # Store chart items for cleanup
        self.chart_items = []
        self.text_items = []

        # Simple crosshair implementation
        self.crosshair_enabled = False
        self.mouse_proxy = None

    def enable_crosshair(self):
        """Enable crosshair functionality with direct mouse tracking."""
        if not self.crosshair_enabled:
            # Create crosshair lines with bright colors and high Z-value to be on top
            self.crosshair_v = pg.InfiniteLine(angle=90, movable=False,
                                             pen=pg.mkPen(color='red', width=5))
            self.crosshair_h = pg.InfiniteLine(angle=0, movable=False,
                                             pen=pg.mkPen(color='red', width=5))

            # Set high Z-value to ensure crosshair is on top
            self.crosshair_v.setZValue(1000)
            self.crosshair_h.setZValue(1000)

            # Position crosshair at center initially to make it visible
            self.crosshair_v.setPos(1)  # Center X position
            self.crosshair_h.setPos(600)  # Approximate center price

            # Add to plot with high priority
            self.addItem(self.crosshair_v, ignoreBounds=True)
            self.addItem(self.crosshair_h, ignoreBounds=True)

            # Create crosshair label with bright background and high Z-value
            self.crosshair_label = pg.TextItem(text="$600.00", color='yellow',
                                             fill=pg.mkBrush(0, 0, 0, 200),
                                             border=pg.mkPen(color='red', width=2))
            self.crosshair_label.setZValue(1001)
            self.crosshair_label.setPos(1.2, 602)  # Position near crosshair
            self.addItem(self.crosshair_label, ignoreBounds=True)

            # Connect to scene mouse move signal
            self.scene().sigMouseMoved.connect(self.on_mouse_moved)

            # Also enable mouse tracking on the widget
            self.setMouseTracking(True)

            # Enable mouse events on the plot item
            self.plotItem.vb.setMouseEnabled(x=True, y=True)

            self.crosshair_enabled = True
            # Crosshair enabled

    def on_mouse_moved(self, pos):
        """Handle mouse movement for crosshair display."""
        if not self.crosshair_enabled:
            return

        try:
            # Check if mouse is within the plot area
            if self.sceneBoundingRect().contains(pos):
                # Convert scene coordinates to view coordinates
                mouse_point = self.plotItem.vb.mapSceneToView(pos)
                x_pos = mouse_point.x()
                y_pos = mouse_point.y()

                # Update crosshair position
                self.crosshair_v.setPos(x_pos)
                self.crosshair_h.setPos(y_pos)

                # Update crosshair label with Y-axis value (price)
                self.crosshair_label.setText(f"${y_pos:.2f}")
                self.crosshair_label.setPos(x_pos + 0.2, y_pos + 2)  # Offset for visibility

            else:
                # Hide crosshair when mouse is outside
                self.crosshair_v.setPos(-10000)
                self.crosshair_h.setPos(-10000)
                self.crosshair_label.setText("")

        except Exception as e:
            print(f"Crosshair error: {e}")
            # Hide crosshair on any error
            if hasattr(self, 'crosshair_v') and self.crosshair_v:
                self.crosshair_v.setPos(-10000)
            if hasattr(self, 'crosshair_h') and self.crosshair_h:
                self.crosshair_h.setPos(-10000)
            if hasattr(self, 'crosshair_label') and self.crosshair_label:
                self.crosshair_label.setText("")

    def calculate_arrow_sizes(self, projected_highs, projected_lows):
        """Calculate dynamic arrow sizes based on projected high/low range.

        Returns:
            tuple: (arrow_y_size, arrow_x_size) where:
                - arrow_y_size is 3% of the height range (highest high to lowest low)
                - arrow_x_size is 0.5% of the visual box range
        """
        try:
            if not projected_highs or not projected_lows:
                return 1.5, 0.05  # Fallback to fixed sizes

            # Calculate the full range from highest projected high to lowest projected low
            highest_high = max(projected_highs)
            lowest_low = min(projected_lows)
            total_range = highest_high - lowest_low

            # Arrow height: 3% of the total price range
            arrow_y_size = total_range * 0.03

            # Arrow width: 0.5% of visual box range (FWLOddsChart: -0.5x to 7.5x = 8 units)
            arrow_x_size = 8 * 0.005 # 0.5% of 8 = 0.08

            return arrow_y_size, arrow_x_size

        except Exception as e:
            print(f"Error calculating arrow sizes: {e}")
            import traceback
            traceback.print_exc()
            return 1.5, 0.05  # Fallback to fixed sizes

    def update_data(self, filtered_high_data, filtered_low_data, market_data=None):
        """Update FWL Odds chart with rectangle, price labels, odds, and last close line."""
        try:
            # Clear existing items (EXACT same as volatility chart)
            self.clear()

            # Enable crosshair after clearing
            self.enable_crosshair()

            if not market_data:
                return

            # Add title to top left (EXACT same as volatility chart)
            try:
                ticker = market_data.get("ticker", "Unknown")
                timeframe = market_data.get("timeframe", "Unknown")
                dtl = market_data.get("dtl", 0)
                length = market_data.get("length", 0)

                # Calculate occurrences using the formula: (occurrences / 2) - 1
                total_occurrences = 0
                if filtered_high_data:
                    total_occurrences += len(filtered_high_data)
                if filtered_low_data:
                    total_occurrences += len(filtered_low_data)

                occurrences = int((total_occurrences / 2) - 1) if total_occurrences > 0 else 0

                # Create multi-line title format
                title_text = f'{ticker} Length: [{length}] {timeframe} Aggregations: {dtl}\noccurrence: {occurrences}'
                title_label = pg.TextItem(title_text, color='white', anchor=(0, 0))  # Left-bottom anchor
                title_label.setFont(QFont("Segoe UI", 8))

                # Position at -0.25x, will be repositioned to max_high after Y-axis limits are set
                title_label.setPos(-0.25, 0)  # Will be repositioned after Y-axis limits are set
                self.addItem(title_label)

                # Store title label reference to reposition it later after Y-axis limits are calculated
                self.title_label = title_label

            except Exception as e:
                print(f"Error adding title to FWL Odds chart: {e}")

            # Extract projected highs and lows
            projected_highs = []
            projected_lows = []

            # Extract projected highs from filtered high data
            if filtered_high_data:
                for row in filtered_high_data:
                    if len(row) > 6:  # Projected High is at index 6
                        try:
                            projected_high_str = str(row[6])
                            if projected_high_str and projected_high_str != '' and projected_high_str != 'None':
                                projected_high = float(projected_high_str)
                                projected_highs.append(projected_high)
                        except (ValueError, TypeError):
                            continue

            # Extract projected lows from filtered low data
            if filtered_low_data:
                for row in filtered_low_data:
                    if len(row) > 6:  # Projected Low is at index 6
                        try:
                            projected_low_str = str(row[6])
                            if projected_low_str and projected_low_str != '' and projected_low_str != 'None':
                                projected_low = float(projected_low_str)
                                projected_lows.append(projected_low)
                        except (ValueError, TypeError):
                            continue

            # Get last close price from market data
            last_close = None
            if market_data and 'close' in market_data:
                try:
                    close_data = market_data['close']
                    if close_data is not None and len(close_data) > 0:
                        last_close = float(close_data[-1])
                except (ValueError, TypeError, IndexError):
                    pass

            # Only proceed if we have both highs and lows
            if not projected_highs or not projected_lows:
                return

            # Draw enhanced rectangle with flow lines and standard deviation markers
            if last_close is not None:
                self.draw_rectangle(projected_highs, projected_lows, last_close, market_data)

            # Add projected high labels at 3.5x
            self.add_price_labels(projected_highs, 3.5, "High")

            # Add projected low labels at 5.5x
            self.add_price_labels(projected_lows, 5.5, "Low")

            # Calculate and add odds for projected highs at 4.5x
            self.add_odds_labels(projected_highs, 4.5, "High")

            # Calculate and add odds for projected lows at 6.5x
            self.add_odds_labels(projected_lows, 6.5, "Low")

            # Add white dotted line for last close price
            if last_close is not None:
                self.draw_last_close_line(last_close)

            # Add arrow lines at 5x and 7x positions
            if last_close is not None and projected_highs and projected_lows:
                self.draw_arrow_lines(projected_highs, projected_lows, last_close)

            # Set appropriate axis ranges (extend to 8.0 to accommodate all elements including 7x lines and labels)
            self.set_axis_ranges(projected_highs, projected_lows, extend_x=8.0)

        except Exception as e:
            print(f"Error updating FWL Odds chart: {e}")

    def clear_chart(self):
        """Clear all chart items."""
        for item in self.chart_items:
            self.removeItem(item)
        for item in self.text_items:
            self.removeItem(item)
        self.chart_items.clear()
        self.text_items.clear()

    def draw_rectangle(self, projected_highs, projected_lows, current_price, market_data=None):
        """Draw enhanced rectangle with green/red flow lines and standard deviation markers."""
        try:
            # Calculate rectangle bounds
            lowest_low = min(projected_lows)
            highest_high = max(projected_highs)

            # Rectangle coordinates: x from 0x to 3x (representing 1% to 50%), y from lowest low to highest high
            x1, x2 = 0.0, 3.0  # 0x to 3x on chart (representing 1% to 50% probability)
            y1, y2 = lowest_low, highest_high

            # Draw rectangle outline
            rect_x = [x1, x2, x2, x1, x1]  # Close the rectangle
            rect_y = [y1, y1, y2, y2, y1]  # Close the rectangle

            rect_item = self.plot(
                rect_x, rect_y,
                pen=pg.mkPen(color='white', width=2),
                name='FWL Rectangle'
            )
            self.chart_items.append(rect_item)

            # Draw flow lines for highs and lows
            self.draw_flow_lines(projected_highs, projected_lows, x1, x2)

            # Draw features in correct layering order (bottom to top)
            # Layer 1 (bottom): Light grey dotted lines
            self.draw_dotted_lines_for_projections(projected_highs, projected_lows)

            # Layer 2: Green and red flow lines
            self.draw_flow_lines(projected_highs, projected_lows, x1, x2)

            # Layer 3: Intersection dot and price labels
            self.draw_intersection_dot(projected_highs, projected_lows)
            self.draw_price_labels_at_05x(current_price, highest_high, lowest_low)
            self.draw_labels_at_25x(projected_highs, projected_lows)

            # Layer 4: White lines (vertical, diagonal, horizontal)
            self.draw_white_lines_at_225x(projected_highs, projected_lows)
            self.draw_lines_from_close_at_25x(current_price, projected_highs, projected_lows)
            self.draw_diagonal_lines_from_25x(projected_highs, projected_lows)

            # Layer 5: White rectangle
            self.draw_rectangle_outline(x1, x2, lowest_low, highest_high)

            # Layer 6 (top): Last close line will be drawn separately

            # Set proper axis ranges
            self.set_fwl_axis_ranges(projected_highs, projected_lows)

        except Exception as e:
            print(f"Error drawing rectangle: {e}")



    def set_fwl_axis_ranges(self, projected_highs, projected_lows):
        """Set axis ranges like volatility chart with proper anchoring."""
        try:
            if not projected_highs or not projected_lows:
                return

            # Calculate range like volatility chart
            all_prices = projected_highs + projected_lows
            min_price = min(all_prices)
            max_price = max(all_prices)
            price_range = max_price - min_price
            padding = price_range * 0.10  # 10% padding like volatility chart

            y_min = min_price - padding
            y_max = max_price + padding

            # Set static axis limits - anchored exactly at -0.5x to 7.5x
            self.setXRange(-0.5, 7.5, padding=0)  # X-axis exactly from -0.5x to 7.5x
            self.setYRange(y_min, y_max, padding=0)  # Y-axis with 10% padding

            # Force exact axis limits without any auto-adjustment
            self.getViewBox().setLimits(xMin=-0.5, xMax=7.5, yMin=y_min, yMax=y_max)

            # Disable auto-ranging to keep it static and anchored like volatility chart
            self.getViewBox().setAutoVisible(x=False, y=False)
            self.getViewBox().enableAutoRange(enable=False)
            self.getViewBox().setMouseEnabled(x=False, y=False)  # Disable mouse interactions
            self.getViewBox().setMenuEnabled(False)  # Disable context menu

            # Reposition title at -0.25x and 7.5% of range above max_high after Y-axis limits are set
            try:
                if hasattr(self, 'title_label') and max_price is not None and min_price is not None:
                    # Calculate 7.5% of the range (max_price - min_price) above max_price
                    range_offset = price_range * 0.075  # 7.5% of the range
                    title_y = max_price + range_offset
                    self.title_label.setPos(-0.25, title_y)

            except Exception as e:
                print(f"Error repositioning title: {e}")

            # Set title_y for section titles
            title_y = max_price + (price_range * 0.075) if max_price is not None and min_price is not None else max_price

            # Add additional titles at same height as main title
            self.add_section_titles(title_y, projected_highs, projected_lows)

        except Exception as e:
            print(f"Error setting FWL axis ranges: {e}")

    def add_section_titles(self, title_y, projected_highs, projected_lows):
        """Add section titles at same height as main title."""
        try:
            # Bullish Moves Price - at exactly 3.5x (left aligned)
            bullish_price_x = 3.5
            bullish_price_label = pg.TextItem('Bullish Moves\nPrice', color='white', anchor=(0, 0))  # Left aligned
            bullish_price_label.setFont(QFont("Segoe UI", 8))
            bullish_price_label.setPos(bullish_price_x, title_y)
            self.addItem(bullish_price_label)
            self.text_items.append(bullish_price_label)

            # Odds for bullish moves - at exactly 4.5x (left aligned)
            bullish_odds_x = 4.5
            bullish_odds_label = pg.TextItem('Odds', color='white', anchor=(0, 0))  # Left aligned
            bullish_odds_label.setFont(QFont("Segoe UI", 8))
            bullish_odds_label.setPos(bullish_odds_x, title_y)
            self.addItem(bullish_odds_label)
            self.text_items.append(bullish_odds_label)

            # Bearish Moves Price - at exactly 5.5x (left aligned, same height as bullish)
            bearish_price_x = 5.5
            bearish_price_label = pg.TextItem('Bearish Moves\nPrice', color='white', anchor=(0, 0))  # Left aligned
            bearish_price_label.setFont(QFont("Segoe UI", 8))
            bearish_price_label.setPos(bearish_price_x, title_y)  # Same height as bullish
            self.addItem(bearish_price_label)
            self.text_items.append(bearish_price_label)

            # Odds for bearish moves - at exactly 6.5x (left aligned, same height as bullish)
            bearish_odds_x = 6.5
            bearish_odds_label = pg.TextItem('Odds', color='white', anchor=(0, 0))  # Left aligned
            bearish_odds_label.setFont(QFont("Segoe UI", 8))
            bearish_odds_label.setPos(bearish_odds_x, title_y)  # Same height as bullish
            self.addItem(bearish_odds_label)
            self.text_items.append(bearish_odds_label)

        except Exception as e:
            print(f"Error adding section titles: {e}")

    def draw_rectangle_outline(self, x1, x2, lowest_low, highest_high):
        """Draw white rectangle outline."""
        try:
            # Draw rectangle outline
            rect_x = [x1, x2, x2, x1, x1]  # Rectangle coordinates
            rect_y = [lowest_low, lowest_low, highest_high, highest_high, lowest_low]

            rectangle_outline = self.plot(
                rect_x, rect_y,
                pen=pg.mkPen(color='white', width=2),
                name='Rectangle Outline'
            )
            self.chart_items.append(rectangle_outline)

        except Exception as e:
            print(f"Error drawing rectangle outline: {e}")

    def draw_flow_lines(self, projected_highs, projected_lows, x1, x2):
        """Draw green and red B-spline flow lines (0x=1%, 2.25x=50%)."""
        try:
            # Sort highs and lows to create flow lines
            sorted_highs = sorted(projected_highs, reverse=True)  # Highest to lowest
            sorted_lows = sorted(projected_lows)  # Lowest to highest

            # Create x-coordinates for flow (0x to 2.25x representing 1% to 50%)
            x_50_percent = 2.25  # 50% is now at 2.25x
            n_highs = len(sorted_highs)
            n_lows = len(sorted_lows)

            # Green B-spline for highs flow (highest prices on left at 0x, flowing to lower prices on right at 2.25x)
            if n_highs > 2:  # Need at least 3 points for B-spline
                x_highs = [x1 + (x_50_percent - x1) * i / (n_highs - 1) for i in range(n_highs)]

                # Create B-spline interpolation with rectangle constraints
                try:
                    from scipy.interpolate import make_interp_spline
                    import numpy as np

                    # Calculate rectangle bounds for constraints
                    all_prices = sorted_highs + sorted_lows
                    min_rect_price = min(all_prices)
                    max_rect_price = max(all_prices)

                    # Create smooth B-spline curve
                    x_smooth = np.linspace(x_highs[0], x_highs[-1], 100)  # 100 points for smooth curve
                    spline = make_interp_spline(x_highs, sorted_highs, k=min(3, n_highs-1))  # Cubic or lower order
                    y_smooth = spline(x_smooth)

                    # Constrain B-spline to stay within rectangle bounds
                    y_smooth = np.clip(y_smooth, min_rect_price, max_rect_price)

                    green_line = self.plot(
                        x_smooth, y_smooth,
                        pen=pg.mkPen(color='green', width=3),
                        name='Highs Flow B-spline (1%-50%)'
                    )
                    self.chart_items.append(green_line)
                except ImportError:
                    # Fallback to linear interpolation if scipy not available
                    green_line = self.plot(
                        x_highs, sorted_highs,
                        pen=pg.mkPen(color='green', width=3),
                        name='Highs Flow (1%-50%)'
                    )
                    self.chart_items.append(green_line)
            elif n_highs == 2:
                # Linear line for 2 points
                x_highs = [x1, x_50_percent]
                green_line = self.plot(
                    x_highs, sorted_highs,
                    pen=pg.mkPen(color='green', width=3),
                    name='Highs Flow (1%-50%)'
                )
                self.chart_items.append(green_line)
            elif n_highs == 1:
                # Single point at middle (1.125x representing ~25%)
                x_mid = (x1 + x_50_percent) / 2
                green_point = self.plot(
                    [x_mid], sorted_highs,
                    pen=pg.mkPen(color='green', width=5),
                    symbol='o',
                    symbolSize=8,
                    name='Single High (~25%)'
                )
                self.chart_items.append(green_point)

            # Red B-spline for lows flow (lowest prices on left at 0x, flowing to higher prices on right at 2.25x)
            if n_lows > 2:  # Need at least 3 points for B-spline
                x_lows = [x1 + (x_50_percent - x1) * i / (n_lows - 1) for i in range(n_lows)]

                # Create B-spline interpolation with rectangle constraints
                try:
                    from scipy.interpolate import make_interp_spline
                    import numpy as np

                    # Calculate rectangle bounds for constraints (reuse from green line calculation)
                    all_prices = sorted_highs + sorted_lows
                    min_rect_price = min(all_prices)
                    max_rect_price = max(all_prices)

                    # Create smooth B-spline curve
                    x_smooth = np.linspace(x_lows[0], x_lows[-1], 100)  # 100 points for smooth curve
                    spline = make_interp_spline(x_lows, sorted_lows, k=min(3, n_lows-1))  # Cubic or lower order
                    y_smooth = spline(x_smooth)

                    # Constrain B-spline to stay within rectangle bounds
                    y_smooth = np.clip(y_smooth, min_rect_price, max_rect_price)

                    red_line = self.plot(
                        x_smooth, y_smooth,
                        pen=pg.mkPen(color='red', width=3),
                        name='Lows Flow B-spline (1%-50%)'
                    )
                    self.chart_items.append(red_line)
                except ImportError:
                    # Fallback to linear interpolation if scipy not available
                    red_line = self.plot(
                        x_lows, sorted_lows,
                        pen=pg.mkPen(color='red', width=3),
                        name='Lows Flow (1%-50%)'
                    )
                    self.chart_items.append(red_line)
            elif n_lows == 2:
                # Linear line for 2 points
                x_lows = [x1, x_50_percent]
                red_line = self.plot(
                    x_lows, sorted_lows,
                    pen=pg.mkPen(color='red', width=3),
                    name='Lows Flow (1%-50%)'
                )
                self.chart_items.append(red_line)
            elif n_lows == 1:
                # Single point at middle (1.125x representing ~25%)
                x_mid = (x1 + x_50_percent) / 2
                red_point = self.plot(
                    [x_mid], sorted_lows,
                    pen=pg.mkPen(color='red', width=5),
                    symbol='o',
                    symbolSize=8,
                    name='Single Low (~25%)'
                )
                self.chart_items.append(red_point)

        except Exception as e:
            print(f"Error drawing flow lines: {e}")

    def draw_intersection_dot(self, projected_highs, projected_lows):
        """Draw a dot at the intersection of green and red B-spline flow lines with price label."""
        try:
            # Sort the data like in flow lines
            sorted_highs = sorted(projected_highs, reverse=True)  # Highest to lowest for green line
            sorted_lows = sorted(projected_lows)  # Lowest to highest for red line

            if len(sorted_highs) < 2 or len(sorted_lows) < 2:
                return  # Need at least 2 points each to create lines

            # Create coordinates for the flow lines (same as in draw_flow_lines)
            x1, x_50_percent = 0, 2.25
            n_highs = len(sorted_highs)
            n_lows = len(sorted_lows)

            # Generate the same B-spline curves as in draw_flow_lines
            try:
                from scipy.interpolate import make_interp_spline
                import numpy as np

                # Calculate rectangle bounds for constraints
                all_prices = sorted_highs + sorted_lows
                min_rect_price = min(all_prices)
                max_rect_price = max(all_prices)

                # Green B-spline (highs flow)
                green_x_coords = [x1 + (x_50_percent - x1) * i / (n_highs - 1) for i in range(n_highs)]
                green_x_smooth = np.linspace(green_x_coords[0], green_x_coords[-1], 100)

                if n_highs > 2:
                    green_spline = make_interp_spline(green_x_coords, sorted_highs, k=min(3, n_highs-1))
                    green_y_smooth = green_spline(green_x_smooth)
                    green_y_smooth = np.clip(green_y_smooth, min_rect_price, max_rect_price)
                else:
                    # Linear interpolation for 2 points
                    green_y_smooth = np.interp(green_x_smooth, green_x_coords, sorted_highs)

                # Red B-spline (lows flow)
                red_x_coords = [x1 + (x_50_percent - x1) * i / (n_lows - 1) for i in range(n_lows)]
                red_x_smooth = np.linspace(red_x_coords[0], red_x_coords[-1], 100)

                if n_lows > 2:
                    red_spline = make_interp_spline(red_x_coords, sorted_lows, k=min(3, n_lows-1))
                    red_y_smooth = red_spline(red_x_smooth)
                    red_y_smooth = np.clip(red_y_smooth, min_rect_price, max_rect_price)
                else:
                    # Linear interpolation for 2 points
                    red_y_smooth = np.interp(red_x_smooth, red_x_coords, sorted_lows)

                # Find intersection by checking where the curves cross
                intersection = self.find_spline_intersection(green_x_smooth, green_y_smooth, red_x_smooth, red_y_smooth)

                if intersection:
                    x_intersect, y_intersect = intersection

                    # Only draw if intersection is within the flow area bounds
                    if 0 <= x_intersect <= 2.25:
                        # Draw intersection dot
                        intersection_dot = self.plot(
                            [x_intersect], [y_intersect],
                            pen=pg.mkPen(color='white', width=3),
                            symbol='o',
                            symbolSize=10,
                            symbolBrush=pg.mkBrush(color='white'),
                            name='Flow Lines Intersection'
                        )
                        self.chart_items.append(intersection_dot)

                        # Add price label above the dot
                        intersection_label = pg.TextItem(
                            f'${y_intersect:.2f}',
                            color='white',
                            anchor=(0.5, 1)  # Center aligned, bottom anchored (appears above dot)
                        )
                        intersection_label.setPos(x_intersect, y_intersect)
                        self.addItem(intersection_label)
                        self.text_items.append(intersection_label)

            except ImportError:
                # Fallback to linear intersection if scipy not available
                green_x_coords = [x1 + (x_50_percent - x1) * i / (n_highs - 1) for i in range(n_highs)]
                red_x_coords = [x1 + (x_50_percent - x1) * i / (n_lows - 1) for i in range(n_lows)]

                intersection = self.find_linear_intersection(green_x_coords, sorted_highs, red_x_coords, sorted_lows)

                if intersection:
                    x_intersect, y_intersect = intersection

                    if 0 <= x_intersect <= 2.25:
                        intersection_dot = self.plot(
                            [x_intersect], [y_intersect],
                            pen=pg.mkPen(color='white', width=3),
                            symbol='o',
                            symbolSize=10,
                            symbolBrush=pg.mkBrush(color='white'),
                            name='Flow Lines Intersection'
                        )
                        self.chart_items.append(intersection_dot)

                        intersection_label = pg.TextItem(
                            f'${y_intersect:.2f}',
                            color='white',
                            anchor=(0.5, 1)
                        )
                        intersection_label.setPos(x_intersect, y_intersect)
                        self.addItem(intersection_label)
                        self.text_items.append(intersection_label)

        except Exception as e:
            print(f"Error drawing intersection dot: {e}")

    def find_spline_intersection(self, x1_smooth, y1_smooth, x2_smooth, y2_smooth):
        """Find intersection point between two B-spline curves."""
        try:
            import numpy as np

            # Find common x range
            x_min = max(x1_smooth[0], x2_smooth[0])
            x_max = min(x1_smooth[-1], x2_smooth[-1])

            if x_min >= x_max:
                return None

            # Create common x grid for comparison
            x_common = np.linspace(x_min, x_max, 200)

            # Interpolate both curves to common x grid
            y1_interp = np.interp(x_common, x1_smooth, y1_smooth)
            y2_interp = np.interp(x_common, x2_smooth, y2_smooth)

            # Find where curves cross (sign change in difference)
            diff = y1_interp - y2_interp

            for i in range(len(diff) - 1):
                if diff[i] * diff[i + 1] <= 0:  # Sign change indicates crossing
                    # Linear interpolation to find exact crossing point
                    if abs(diff[i + 1] - diff[i]) > 1e-10:
                        t = -diff[i] / (diff[i + 1] - diff[i])
                        x_intersect = x_common[i] + t * (x_common[i + 1] - x_common[i])
                        y_intersect = y1_interp[i] + t * (y1_interp[i + 1] - y1_interp[i])
                        return (x_intersect, y_intersect)

            return None

        except Exception as e:
            print(f"Error finding spline intersection: {e}")
            return None

    def find_linear_intersection(self, x1_coords, y1_coords, x2_coords, y2_coords):
        """Find intersection point between two linear lines (fallback method)."""
        try:
            # For linear lines, find intersection of the two line segments
            for i in range(len(x1_coords) - 1):
                for j in range(len(x2_coords) - 1):
                    intersection = self.calculate_segment_intersection(
                        x1_coords[i], y1_coords[i], x1_coords[i + 1], y1_coords[i + 1],
                        x2_coords[j], y2_coords[j], x2_coords[j + 1], y2_coords[j + 1]
                    )
                    if intersection:
                        return intersection
            return None

        except Exception as e:
            print(f"Error finding linear intersection: {e}")
            return None

    def calculate_segment_intersection(self, x1, y1, x2, y2, x3, y3, x4, y4):
        """Calculate intersection point between two line segments."""
        try:
            # Calculate the direction vectors
            denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)

            # If denominator is 0, lines are parallel
            if abs(denom) < 1e-10:
                return None

            # Calculate intersection parameters
            t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
            u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom

            # Check if intersection is within both line segments
            if 0 <= t <= 1 and 0 <= u <= 1:
                # Calculate intersection point
                intersection_x = x1 + t * (x2 - x1)
                intersection_y = y1 + t * (y2 - y1)
                return (intersection_x, intersection_y)

            return None

        except Exception as e:
            print(f"Error calculating segment intersection: {e}")
            return None

    def draw_white_lines_at_225x(self, projected_highs, projected_lows):
        """Draw white lines at 2.25x for lowest projected high and highest projected low till 2.75x."""
        try:
            if not projected_highs or not projected_lows:
                return
            lowest_projected_high = min(projected_highs)
            highest_projected_low = max(projected_lows)

            # White line for lowest projected high from 2.25x to 2.75x
            self.chart_items.append(self.plot(
                [2.25, 2.75], [lowest_projected_high, lowest_projected_high],
                pen=pg.mkPen(color='white', width=2), name='Lowest Projected High'
            ))

            # White line for highest projected low from 2.25x to 2.75x
            self.chart_items.append(self.plot(
                [2.25, 2.75], [highest_projected_low, highest_projected_low],
                pen=pg.mkPen(color='white', width=2), name='Highest Projected Low'
            ))
        except Exception as e:
            print(f"Error drawing white lines at 2.25x: {e}")

    def draw_lines_from_close_at_25x(self, current_price, projected_highs, projected_lows):
        """Draw vertical white lines at 2.5x from last close to highest projected low and lowest projected high with arrows."""
        try:
            highest_projected_low = max(projected_lows)
            lowest_projected_high = min(projected_highs)

            # Vertical line at 2.5x from last close to highest projected low
            close_to_high_low = self.plot(
                [2.5, 2.5], [current_price, highest_projected_low],  # Vertical line at 2.5x
                pen=pg.mkPen(color='white', width=2),
                name='Close to Highest Low at 2.5x'
            )
            self.chart_items.append(close_to_high_low)

            # Add arrow pointing at highest projected low - dynamic sizing, filled white
            arrow_y_size, arrow_x_size = self.calculate_arrow_sizes(projected_highs, projected_lows)
            if highest_projected_low > current_price:
                # Arrow pointing up
                arrow_high_low = self.plot(
                    [2.5, 2.5 - arrow_x_size, 2.5 + arrow_x_size, 2.5], [highest_projected_low, highest_projected_low - arrow_y_size, highest_projected_low - arrow_y_size, highest_projected_low],
                    pen=pg.mkPen(color='white', width=1),
                    brush=pg.mkBrush(color='white'),
                    fillLevel=0,
                    name='Arrow to Highest Low'
                )
            else:
                # Arrow pointing down
                arrow_high_low = self.plot(
                    [2.5, 2.5 - arrow_x_size, 2.5 + arrow_x_size, 2.5], [highest_projected_low, highest_projected_low + arrow_y_size, highest_projected_low + arrow_y_size, highest_projected_low],
                    pen=pg.mkPen(color='white', width=1),
                    brush=pg.mkBrush(color='white'),
                    fillLevel=0,
                    name='Arrow to Highest Low'
                )
            self.chart_items.append(arrow_high_low)

            # Vertical line at 2.5x from last close to lowest projected high
            close_to_low_high = self.plot(
                [2.5, 2.5], [current_price, lowest_projected_high],  # Vertical line at 2.5x
                pen=pg.mkPen(color='white', width=2),
                name='Close to Lowest High at 2.5x'
            )
            self.chart_items.append(close_to_low_high)

            # Add arrow pointing at lowest projected high - dynamic sizing, filled white
            if lowest_projected_high > current_price:
                # Arrow pointing up
                arrow_low_high = self.plot(
                    [2.5, 2.5 - arrow_x_size, 2.5 + arrow_x_size, 2.5], [lowest_projected_high, lowest_projected_high - arrow_y_size, lowest_projected_high - arrow_y_size, lowest_projected_high],
                    pen=pg.mkPen(color='white', width=1),
                    brush=pg.mkBrush(color='white'),
                    fillLevel=0,
                    name='Arrow to Lowest High'
                )
            else:
                # Arrow pointing down
                arrow_low_high = self.plot(
                    [2.5, 2.5 - arrow_x_size, 2.5 + arrow_x_size, 2.5], [lowest_projected_high, lowest_projected_high + arrow_y_size, lowest_projected_high + arrow_y_size, lowest_projected_high],
                    pen=pg.mkPen(color='white', width=1),
                    brush=pg.mkBrush(color='white'),
                    fillLevel=0,
                    name='Arrow to Lowest High'
                )
            self.chart_items.append(arrow_low_high)

        except Exception as e:
            print(f"Error drawing lines from close at 2.5x: {e}")

    def draw_diagonal_lines_from_25x(self, projected_highs, projected_lows):
        """Draw diagonal lines from specific points at 2.5x to specific points at 0x with arrows."""
        try:
            highest_projected_low = max(projected_lows)
            lowest_projected_high = min(projected_highs)
            highest_projected_high = max(projected_highs)
            lowest_projected_low = min(projected_lows)

            # Diagonal line from highest projected low at 2.5x to highest projected high at 0x
            diag_line_1 = self.plot(
                [2.5, 0], [highest_projected_low, highest_projected_high],
                pen=pg.mkPen(color='white', width=2),
                name='Diagonal: Highest Low (2.5x) to Highest High (0x)'
            )
            self.chart_items.append(diag_line_1)

            # White diagonal line 1: from highest projected low at 2.5x to highest projected high at 0x
            diag_line_1 = self.plot(
                [2.5, 0], [highest_projected_low, highest_projected_high],
                pen=pg.mkPen(color='white', width=2),
                name='White Diagonal: Highest Low (2.5x) to Highest High (0x)'
            )
            self.chart_items.append(diag_line_1)

            # Simple arrow at 0x end of diagonal line 1 - dynamic sizing
            arrow_y_size, arrow_x_size = self.calculate_arrow_sizes(projected_highs, projected_lows)
            arrow_diag_1 = self.plot(
                [0, 0 - arrow_x_size, 0 + arrow_x_size, 0],
                [highest_projected_high, highest_projected_high - arrow_y_size, highest_projected_high - arrow_y_size, highest_projected_high],
                pen=pg.mkPen(color='white', width=1),
                brush=pg.mkBrush(color='white'),
                fillLevel=0,
                name='White Diagonal Arrow 1'
            )
            self.chart_items.append(arrow_diag_1)

            # White diagonal line 2: from lowest projected high at 2.5x to lowest projected low at 0x
            diag_line_2 = self.plot(
                [2.5, 0], [lowest_projected_high, lowest_projected_low],
                pen=pg.mkPen(color='white', width=2),
                name='White Diagonal: Lowest High (2.5x) to Lowest Low (0x)'
            )
            self.chart_items.append(diag_line_2)

            # Simple arrow at 0x end of diagonal line 2 - dynamic sizing (reuse calculated values)
            arrow_diag_2 = self.plot(
                [0, 0 - arrow_x_size, 0 + arrow_x_size, 0],
                [lowest_projected_low, lowest_projected_low + arrow_y_size, lowest_projected_low + arrow_y_size, lowest_projected_low],
                pen=pg.mkPen(color='white', width=1),
                brush=pg.mkBrush(color='white'),
                fillLevel=0,
                name='White Diagonal Arrow 2'
            )
            self.chart_items.append(arrow_diag_2)

        except Exception as e:
            print(f"Error drawing diagonal lines from 2.5x: {e}")

    def draw_price_labels_at_05x(self, current_price, highest_high, lowest_low):
        """Draw 25%, 50%, 75% price labels at 0.5x from last close to extremes."""
        try:
            # Calculate 25%, 50%, 75% from last close to highest projected high
            high_diff = highest_high - current_price
            high_25_pct = current_price + (high_diff * 0.25)
            high_50_pct = current_price + (high_diff * 0.50)
            high_75_pct = current_price + (high_diff * 0.75)

            # Calculate 25%, 50%, 75% from last close to lowest projected low
            low_diff = lowest_low - current_price
            low_25_pct = current_price + (low_diff * 0.25)
            low_50_pct = current_price + (low_diff * 0.50)
            low_75_pct = current_price + (low_diff * 0.75)

            # Add price labels for high percentages (above current price)
            if high_diff > 0:
                for pct, price in [(25, high_25_pct), (50, high_50_pct), (75, high_75_pct)]:
                    label = pg.TextItem(f'${price:.2f}', color='white', anchor=(0, 0.5))
                    label.setPos(0.5, price)
                    self.addItem(label)
                    self.text_items.append(label)

            # Add price labels for low percentages (below current price)
            if low_diff < 0:
                for pct, price in [(25, low_25_pct), (50, low_50_pct), (75, low_75_pct)]:
                    label = pg.TextItem(f'${price:.2f}', color='white', anchor=(0, 0.5))
                    label.setPos(0.5, price)
                    self.addItem(label)
                    self.text_items.append(label)

        except Exception as e:
            print(f"Error drawing price labels at 0.5x: {e}")

    def draw_dotted_lines_for_projections(self, projected_highs, projected_lows):
        """Draw light grey dotted lines between 0x to 3x for every projected high and low."""
        try:
            # Draw dotted lines for all projected highs using PyQtGraph's dotted style
            for high in projected_highs:
                if high is not None and not (isinstance(high, float) and (high != high)):  # Check for None and NaN
                    # Use PyQtGraph's built-in dotted line style
                    dotted_line_high = self.plot(
                        [0, 3], [high, high],
                        pen=pg.mkPen(color='lightgrey', width=1, style=pg.QtCore.Qt.PenStyle.DotLine),
                        name=f'Dotted High: {high:.2f}'
                    )
                    self.chart_items.append(dotted_line_high)

            # Draw dotted lines for all projected lows using PyQtGraph's dotted style
            for low in projected_lows:
                if low is not None and not (isinstance(low, float) and (low != low)):  # Check for None and NaN
                    # Use PyQtGraph's built-in dotted line style
                    dotted_line_low = self.plot(
                        [0, 3], [low, low],
                        pen=pg.mkPen(color='lightgrey', width=1, style=pg.QtCore.Qt.PenStyle.DotLine),
                        name=f'Dotted Low: {low:.2f}'
                    )
                    self.chart_items.append(dotted_line_low)

        except Exception as e:
            print(f"Error drawing dotted lines for projections: {e}")

    def draw_labels_at_25x(self, projected_highs, projected_lows):
        """Draw labels at 2.5x for standard price range and outliers."""
        try:
            highest_projected_high = max(projected_highs)
            lowest_projected_high = min(projected_highs)
            highest_projected_low = max(projected_lows)
            lowest_projected_low = min(projected_lows)

            # Standard price range: mean of highest projected low and lowest projected high
            standard_range_price = (highest_projected_low + lowest_projected_high) / 2
            standard_label = pg.TextItem(
                'Standard\nPrice\nRange',
                color='white',
                anchor=(0.5, 0.5)  # Center aligned
            )
            standard_label.setPos(2.5, standard_range_price)
            self.addItem(standard_label)
            self.text_items.append(standard_label)

            # Upper outlier: mean of highest projected high and highest projected low
            upper_outlier_price = (highest_projected_high + highest_projected_low) / 2
            upper_outlier_label = pg.TextItem(
                'Outlier',
                color='white',
                anchor=(0.5, 0.5)  # Center aligned
            )
            upper_outlier_label.setPos(2.5, upper_outlier_price)
            self.addItem(upper_outlier_label)
            self.text_items.append(upper_outlier_label)

            # Lower outlier: mean of lowest projected high and lowest projected low
            lower_outlier_price = (lowest_projected_high + lowest_projected_low) / 2
            lower_outlier_label = pg.TextItem(
                'Outlier',
                color='white',
                anchor=(0.5, 0.5)  # Center aligned
            )
            lower_outlier_label.setPos(2.5, lower_outlier_price)
            self.addItem(lower_outlier_label)
            self.text_items.append(lower_outlier_label)

        except Exception as e:
            print(f"Error drawing labels at 2.5x: {e}")



    def draw_stdev_markers(self, current_price, highest_high, lowest_low, x1, x2):
        """Draw 25%, 50%, 75% standard deviation markers from current price to extremes (0x-3x coords)."""
        try:

            # Calculate standard deviations from current price to highest high
            high_diff = highest_high - current_price
            high_25_pct = current_price + (high_diff * 0.25)
            high_50_pct = current_price + (high_diff * 0.50)
            high_75_pct = current_price + (high_diff * 0.75)

            # Calculate standard deviations from current price to lowest low
            low_diff = lowest_low - current_price
            low_25_pct = current_price + (low_diff * 0.25)
            low_50_pct = current_price + (low_diff * 0.50)
            low_75_pct = current_price + (low_diff * 0.75)

            # Draw horizontal lines for high standard deviations (above current price)
            if high_diff > 0:
                for pct, price in [(25, high_25_pct), (50, high_50_pct), (75, high_75_pct)]:
                    # Use simple solid lines for now
                    line = self.plot(
                        [x1, x2], [price, price],
                        pen=pg.mkPen(color='lightgreen', width=1),
                        name=f'High {pct}% StDev'
                    )
                    self.chart_items.append(line)

                    # Add label positioned just outside the rectangle at 3.2x
                    label = pg.TextItem(f'{pct}%', color='lightgreen', anchor=(0, 0.5))
                    label.setPos(3.2, price)
                    self.addItem(label)
                    self.text_items.append(label)

            # Draw horizontal lines for low standard deviations (below current price)
            if low_diff < 0:
                for pct, price in [(25, low_25_pct), (50, low_50_pct), (75, low_75_pct)]:
                    # Use simple solid lines for now
                    line = self.plot(
                        [x1, x2], [price, price],
                        pen=pg.mkPen(color='lightcoral', width=1),
                        name=f'Low {pct}% StDev'
                    )
                    self.chart_items.append(line)

                    # Add label positioned just outside the rectangle at 3.2x
                    label = pg.TextItem(f'{pct}%', color='lightcoral', anchor=(0, 0.5))
                    label.setPos(3.2, price)
                    self.addItem(label)
                    self.text_items.append(label)

        except Exception as e:
            print(f"Error drawing standard deviation markers: {e}")

    def add_price_labels(self, prices, x_position, label_type):
        """Add price labels at the specified x position."""
        try:
            for price in prices:
                # Create text label
                label_text = f"{price:.2f}"
                text_item = pg.TextItem(
                    text=label_text,
                    color='white',
                    anchor=(0, 0.5)  # Left-aligned, vertically centered
                )
                text_item.setPos(x_position, price)

                # Add to chart
                self.addItem(text_item)
                self.text_items.append(text_item)

        except Exception as e:
            print(f"Error adding {label_type} labels: {e}")

    def add_odds_labels(self, prices, x_position, label_type):
        """Add odds percentage labels at the specified x position."""
        try:
            if not prices:
                return

            # Sort prices for consistent odds calculation
            sorted_prices = sorted(prices)

            # Calculate odds based on rank/position, not price value
            n_prices = len(sorted_prices)

            for price in prices:
                if n_prices == 1:
                    percentage = 25.5  # Single value gets midpoint
                else:
                    # Find the rank/position of this price in the sorted list
                    price_rank = sorted_prices.index(price)

                    if label_type == "High":
                        # For highs: highest (last in sorted list) = 1%, lowest (first in sorted list) = 50%
                        # Reverse the rank so highest price gets rank 0
                        reversed_rank = n_prices - 1 - price_rank
                        ratio = reversed_rank / (n_prices - 1)
                        percentage = 1.0 + (ratio * 49.0)  # 1% to 50%
                    else:  # label_type == "Low"
                        # For lows: lowest (first in sorted list) = 1%, highest (last in sorted list) = 50%
                        ratio = price_rank / (n_prices - 1)
                        percentage = 1.0 + (ratio * 49.0)  # 1% to 50%

                # Create odds label
                label_text = f"{percentage:.1f}%"
                text_item = pg.TextItem(
                    text=label_text,
                    color='white',
                    anchor=(0, 0.5)  # Left-aligned, vertically centered
                )
                text_item.setPos(x_position, price)

                # Add to chart
                self.addItem(text_item)
                self.text_items.append(text_item)

        except Exception as e:
            print(f"Error adding {label_type} odds labels: {e}")

    def draw_last_close_line(self, last_close):
        """Draw a white dotted line at the last close price from -0.5x to 7.5x with current price label."""
        try:
            # Create line data points (start from -0.5x instead of 0x)
            line_x = [-0.5, 7.5]
            line_y = [last_close, last_close]

            # Create dotted line
            line_item = self.plot(
                line_x, line_y,
                pen=pg.mkPen(color='white', width=1, style=pg.QtCore.Qt.PenStyle.DotLine),
                name='Last Close'
            )
            self.chart_items.append(line_item)

            # Add current price label (left aligned above the line)
            current_price_label = pg.TextItem(
                f'Current Price:\n${last_close:.2f}',
                color='white',
                anchor=(0, 1)  # Left aligned, bottom anchored (so it appears above the line)
            )
            current_price_label.setPos(-0.5, last_close)  # Position at start of line
            self.addItem(current_price_label)
            self.text_items.append(current_price_label)

        except Exception as e:
            print(f"Error drawing last close line: {e}")

    def draw_arrow_lines(self, projected_highs, projected_lows, current_price):
        """Draw vertical lines at 5x and 7x positions that go to the current price level."""
        try:
            # Find highest and lowest projected highs
            highest_projected_high = max(projected_highs)
            lowest_projected_high = min(projected_highs)

            # Find highest and lowest projected lows
            highest_projected_low = max(projected_lows)
            lowest_projected_low = min(projected_lows)

            # At 5x: Red vertical line from highest projected high to current price level
            red_line_5x = self.plot(
                [5, 5], [highest_projected_high, current_price],
                pen=pg.mkPen(color='red', width=2),
                name=f'Red Line 5x: {highest_projected_high:.2f} to {current_price:.2f}'
            )
            self.chart_items.append(red_line_5x)

            # Add filled red arrow pointing at current price (at 5x position) - dynamic sizing
            arrow_y_size, arrow_x_size = self.calculate_arrow_sizes(projected_highs, projected_lows)
            if highest_projected_high > current_price:
                # Arrow pointing down (line goes from high to low)
                red_arrow_5x = self.plot(
                    [5, 5 - arrow_x_size, 5 + arrow_x_size, 5], [current_price, current_price + arrow_y_size, current_price + arrow_y_size, current_price],
                    pen=pg.mkPen(color='red', width=1),
                    brush=pg.mkBrush(color='red'),
                    fillLevel=0,
                    name='Red Arrow 5x'
                )
            else:
                # Arrow pointing up (line goes from low to high)
                red_arrow_5x = self.plot(
                    [5, 5 - arrow_x_size, 5 + arrow_x_size, 5], [current_price, current_price - arrow_y_size, current_price - arrow_y_size, current_price],
                    pen=pg.mkPen(color='red', width=1),
                    brush=pg.mkBrush(color='red'),
                    fillLevel=0,
                    name='Red Arrow 5x'
                )
            self.chart_items.append(red_arrow_5x)

            # At 5x: Green vertical line from lowest projected high to current price level
            green_line_5x = self.plot(
                [5, 5], [lowest_projected_high, current_price],
                pen=pg.mkPen(color='green', width=2),
                name=f'Green Line 5x: {lowest_projected_high:.2f} to {current_price:.2f}'
            )
            self.chart_items.append(green_line_5x)

            # Add filled green arrow pointing at current price (at 5x position) - dynamic sizing (reuse calculated values)
            if lowest_projected_high > current_price:
                # Arrow pointing down (line goes from high to low)
                green_arrow_5x = self.plot(
                    [5, 5 - arrow_x_size, 5 + arrow_x_size, 5], [current_price, current_price + arrow_y_size, current_price + arrow_y_size, current_price],
                    pen=pg.mkPen(color='green', width=1),
                    brush=pg.mkBrush(color='green'),
                    fillLevel=0,
                    name='Green Arrow 5x'
                )
            else:
                # Arrow pointing up (line goes from low to high)
                green_arrow_5x = self.plot(
                    [5, 5 - arrow_x_size, 5 + arrow_x_size, 5], [current_price, current_price - arrow_y_size, current_price - arrow_y_size, current_price],
                    pen=pg.mkPen(color='green', width=1),
                    brush=pg.mkBrush(color='green'),
                    fillLevel=0,
                    name='Green Arrow 5x'
                )
            self.chart_items.append(green_arrow_5x)

            # At 7x: Red vertical line from lowest projected low to current price level
            red_line_7x = self.plot(
                [7, 7], [lowest_projected_low, current_price],
                pen=pg.mkPen(color='red', width=2),
                name=f'Red Line 7x: {lowest_projected_low:.2f} to {current_price:.2f}'
            )
            self.chart_items.append(red_line_7x)

            # Add filled red arrow pointing at current price (at 7x position) - dynamic sizing (reuse calculated values)
            if lowest_projected_low > current_price:
                # Arrow pointing down (line goes from high to low)
                red_arrow_7x = self.plot(
                    [7, 7 - arrow_x_size, 7 + arrow_x_size, 7], [current_price, current_price + arrow_y_size, current_price + arrow_y_size, current_price],
                    pen=pg.mkPen(color='red', width=1),
                    brush=pg.mkBrush(color='red'),
                    fillLevel=0,
                    name='Red Arrow 7x'
                )
            else:
                # Arrow pointing up (line goes from low to high)
                red_arrow_7x = self.plot(
                    [7, 7 - arrow_x_size, 7 + arrow_x_size, 7], [current_price, current_price - arrow_y_size, current_price - arrow_y_size, current_price],
                    pen=pg.mkPen(color='red', width=1),
                    brush=pg.mkBrush(color='red'),
                    fillLevel=0,
                    name='Red Arrow 7x'
                )
            self.chart_items.append(red_arrow_7x)

            # At 7x: Green vertical line from highest projected low to current price level
            green_line_7x = self.plot(
                [7, 7], [highest_projected_low, current_price],
                pen=pg.mkPen(color='green', width=2),
                name=f'Green Line 7x: {highest_projected_low:.2f} to {current_price:.2f}'
            )
            self.chart_items.append(green_line_7x)

            # Add filled green arrow pointing at current price (at 7x position) - dynamic sizing (reuse calculated values)
            if highest_projected_low > current_price:
                # Arrow pointing down (line goes from high to low)
                green_arrow_7x = self.plot(
                    [7, 7 - arrow_x_size, 7 + arrow_x_size, 7], [current_price, current_price + arrow_y_size, current_price + arrow_y_size, current_price],
                    pen=pg.mkPen(color='green', width=1),
                    brush=pg.mkBrush(color='green'),
                    fillLevel=0,
                    name='Green Arrow 7x'
                )
            else:
                # Arrow pointing up (line goes from low to high)
                green_arrow_7x = self.plot(
                    [7, 7 - arrow_x_size, 7 + arrow_x_size, 7], [current_price, current_price - arrow_y_size, current_price - arrow_y_size, current_price],
                    pen=pg.mkPen(color='green', width=1),
                    brush=pg.mkBrush(color='green'),
                    fillLevel=0,
                    name='Green Arrow 7x'
                )
            self.chart_items.append(green_arrow_7x)

        except Exception as e:
            print(f"Error drawing arrow lines: {e}")

    def set_axis_ranges(self, projected_highs, projected_lows, extend_x=8.0):
        """Set appropriate axis ranges for the chart."""
        try:
            # Calculate price range with some padding
            all_prices = projected_highs + projected_lows
            min_price = min(all_prices)
            max_price = max(all_prices)
            price_range = max_price - min_price
            padding = price_range * 0.1  # 10% padding

            # Set Y axis range
            self.setYRange(min_price - padding, max_price + padding)

            # Set X axis range to show from 0 to extend_x (covers all elements including dotted line)
            self.setXRange(0, extend_x)

        except Exception as e:
            print(f"Error setting axis ranges: {e}")


class VolatilityStatisticsTab(QWidget):
    """Volatility Statistics tab with unified state management across all subtabs."""

    # Class variables to track settings state across all instances
    _settings_expanded = False
    _all_settings_buttons = []
    _all_settings_content = []

    # Unified filter state management across all subtabs
    _unified_filter_type = 0  # Default to H/L matching (0 = H/L matching, 1 = weekday matching, -1 = no filter)
    _all_filter_button_groups = []  # Track all filter button groups for synchronization
    _all_volatility_tabs = []  # Track all volatility tab instances



    def __init__(self, data_service=None, parent=None):
        super().__init__(parent)
        self.data_service = data_service

        # Options service will be accessed through data_service

        # Add this instance to the global list for unified state management
        VolatilityStatisticsTab._all_volatility_tabs.append(self)

        # Store original data for filtering
        self._original_high_data = []
        self._original_low_data = []

        # Shared options data cache for all charts in this tab instance
        self._shared_options_data = None
        self._shared_options_timestamp = 0

        # Initialize FWL Aggr debounce timer
        self.fwl_aggr_timer = QTimer()
        self.fwl_aggr_timer.setSingleShot(True)
        self.fwl_aggr_timer.timeout.connect(self._trigger_data_refresh)
        self._main_window_ref = None

        # Initialize cached zones data
        self._cached_zones_data = None

        # Historical data tracking variables (similar to example_volstattab.py)
        self.viewing_historical = False
        self.original_data = None
        self.original_rebased_data = None
        self.original_categories = None
        self.historical_timestamp = None
        self.historical_index = None
        self.historical_day_high = None
        self.historical_day_low = None

        self.setup_ui()

    def setup_ui(self):
        """Initialize the user interface with unified state management across subtabs."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Create main horizontal splitter for content and shared settings
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setChildrenCollapsible(False)
        main_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #2b2b2b;
                width: 2px;
            }
            QSplitter::handle:hover {
                background-color: #2b2b2b;
            }
        """)

        # Create tab widget for left side (3/4 width)
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #2b2b2b;
                background-color: #1e1e1e;
                border-top: none;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #1e1e1e;
                color: #ffffff;
                border: none;
                padding: 4px 12px;
                margin-right: 2px;
                min-width: 60px;
                max-height: 24px;
            }
            QTabBar::tab:selected {
                background-color: #2b2b2b;
                border: 1px solid #1e1e1e;
                border-bottom: none;
            }
            QTabBar::tab:hover:!selected {
                background-color: #3c3c3c;
            }
        """)

        # Set the tab bar height to be smaller
        self.tab_widget.tabBar().setFixedHeight(26)

        # Create Chart tab (no internal settings panel)
        self.chart_tab = self.create_chart_tab()
        self.tab_widget.addTab(self.chart_tab, "Chart")

        # Create Data tab (no internal settings panel)
        self.data_tab = self.create_data_tab()
        self.tab_widget.addTab(self.data_tab, "Data")

        # Create unified shared settings panel for right side (1/4 width)
        self.unified_settings_widget = self.create_unified_settings_panel()

        # Add widgets to main splitter
        main_splitter.addWidget(self.tab_widget)
        main_splitter.addWidget(self.unified_settings_widget)

        # Set the sizes for 3/4 and 1/4 split
        main_splitter.setSizes([750, 250])
        main_splitter.setStretchFactor(0, 3)
        main_splitter.setStretchFactor(1, 1)

        # Disable handle movement for fixed split
        handle = main_splitter.handle(1)
        handle.setEnabled(False)

        layout.addWidget(main_splitter)

    def create_unified_settings_panel(self):
        """Create the unified settings panel that controls all subtabs."""
        # Create container widget with top margin to align with tab content area
        container_widget = QWidget()
        container_layout = QVBoxLayout(container_widget)
        container_layout.setContentsMargins(0, 26, 0, 0)  # 26px top margin to match tab bar height
        container_layout.setSpacing(0)

        settings_widget = QWidget()
        settings_widget.setStyleSheet("border: 2px solid #2b2b2b; background-color: #1e1e1e;")

        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Create collapsible settings button
        self.unified_settings_button = QPushButton("▶ Settings")
        self.unified_settings_button.setFixedHeight(30)
        self.unified_settings_button.setFont(QFont("Segoe UI", 10))

        # Style the settings button to match dark theme with left border
        self.unified_settings_button.setStyleSheet("""
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                border-left: 3px solid #555555;
                text-align: left;
                padding-left: 10px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
        """)

        # Create settings content area (initially hidden)
        self.unified_settings_content = QWidget()
        self.unified_settings_content.setVisible(self._settings_expanded)

        # Settings content layout with 1/2 horizontal split
        settings_layout = QVBoxLayout()
        settings_layout.setContentsMargins(5, 5, 5, 5)
        settings_layout.setSpacing(0)

        # Create horizontal splitter for settings 1/2 split (left/right)
        settings_splitter = QSplitter(Qt.Orientation.Horizontal)
        settings_splitter.setChildrenCollapsible(False)
        settings_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #555555;
                width: 2px;
            }
        """)

        # Left section (1/2) - FWL Aggr. setting
        left_settings = QWidget()
        left_settings.setStyleSheet("background-color: #1e1e1e;")
        left_layout = QVBoxLayout()
        left_layout.setContentsMargins(8, 8, 8, 8)
        left_layout.setSpacing(8)

        # Add Aggregation title
        aggregation_title = QLabel("Aggregation")
        aggregation_title.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        aggregation_title.setStyleSheet("color: #ffffff; margin-bottom: 5px;")
        aggregation_title.setFixedHeight(25)  # Fixed height for alignment
        left_layout.addWidget(aggregation_title)

        # FWL Aggr. setting
        fwl_aggr_layout = QHBoxLayout()
        fwl_aggr_layout.setSpacing(5)
        fwl_aggr_layout.setContentsMargins(0, 0, 0, 0)

        # FWL Aggr. label - match radio button styling
        fwl_aggr_label = QLabel("FWL Aggr.:")
        fwl_aggr_label.setFont(QFont("Segoe UI", 9))
        fwl_aggr_label.setStyleSheet("color: #ffffff; border: none; outline: none;")
        fwl_aggr_label.setAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft)
        fwl_aggr_label.setMinimumHeight(20)  # Match radio button height
        fwl_aggr_label.setFixedWidth(120)  # Wider to match occurrence limiter

        # FWL Aggr. custom number input with up/down buttons
        fwl_input_container = QWidget()
        fwl_input_layout = QHBoxLayout()
        fwl_input_layout.setContentsMargins(0, 0, 0, 0)
        fwl_input_layout.setSpacing(2)

        # Text input field
        self.fwl_aggr_input = QLineEdit()
        self.fwl_aggr_input.setText("1")  # Default value
        self.fwl_aggr_input.setFixedSize(25, 18)  # Smaller and more compact
        self.fwl_aggr_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        input_font = QFont("Segoe UI", 8)  # Smaller font
        self.fwl_aggr_input.setFont(input_font)

        # Up/Down button container
        button_container = QWidget()
        button_layout = QVBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(0)

        # Up button
        self.fwl_up_button = QPushButton("▲")
        self.fwl_up_button.setFixedSize(14, 9)  # Even smaller buttons
        button_font = QFont("Segoe UI", 6)  # Slightly larger font for readability
        self.fwl_up_button.setFont(button_font)

        # Down button
        self.fwl_down_button = QPushButton("▼")
        self.fwl_down_button.setFixedSize(14, 9)  # Even smaller buttons
        self.fwl_down_button.setFont(button_font)

        # Add buttons to button layout
        button_layout.addWidget(self.fwl_up_button)
        button_layout.addWidget(self.fwl_down_button)
        button_container.setLayout(button_layout)

        # Add input and buttons to container
        fwl_input_layout.addWidget(self.fwl_aggr_input)
        fwl_input_layout.addWidget(button_container)
        fwl_input_container.setLayout(fwl_input_layout)
        fwl_input_container.setFixedSize(45, 18)  # Compact container to match input height

        # Connect button signals
        self.fwl_up_button.clicked.connect(self._increment_fwl_aggr)
        self.fwl_down_button.clicked.connect(self._decrement_fwl_aggr)

        # Connect text change to validation and data refresh
        self.fwl_aggr_input.textChanged.connect(self._validate_fwl_aggr_input)
        self.fwl_aggr_input.textChanged.connect(self._on_fwl_aggr_changed)

        # Add to horizontal layout
        fwl_aggr_layout.addWidget(fwl_aggr_label)
        fwl_aggr_layout.addWidget(fwl_input_container)
        fwl_aggr_layout.addStretch()  # Push to left

        # Add to left layout
        left_layout.addLayout(fwl_aggr_layout)

        # Occurrence Limiter setting
        occurrence_layout = QHBoxLayout()
        occurrence_layout.setSpacing(5)
        occurrence_layout.setContentsMargins(0, 0, 0, 0)

        # Occurrence Limiter label - match radio button styling
        occurrence_label = QLabel("Occurrence Limiter:")
        occurrence_label.setFont(QFont("Segoe UI", 9))
        occurrence_label.setStyleSheet("color: #ffffff; border: none; outline: none;")
        occurrence_label.setAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft)
        occurrence_label.setMinimumHeight(20)  # Match radio button height
        occurrence_label.setFixedWidth(120)  # Match FWL Aggr label width

        # Occurrence Limiter custom number input with up/down buttons
        occurrence_input_container = QWidget()
        occurrence_input_layout = QHBoxLayout()
        occurrence_input_layout.setContentsMargins(0, 0, 0, 0)
        occurrence_input_layout.setSpacing(2)

        # Text input field
        self.occurrence_input = QLineEdit()
        self.occurrence_input.setText("0")  # Default value (0 = all rows)
        self.occurrence_input.setFixedSize(25, 18)  # Match FWL Aggr input size
        self.occurrence_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.occurrence_input.setFont(input_font)

        # Up/Down button container for occurrence
        occurrence_button_container = QWidget()
        occurrence_button_layout = QVBoxLayout()
        occurrence_button_layout.setContentsMargins(0, 0, 0, 0)
        occurrence_button_layout.setSpacing(0)

        # Up button for occurrence
        self.occurrence_up_button = QPushButton("▲")
        self.occurrence_up_button.setFixedSize(14, 9)  # Match FWL Aggr button size
        self.occurrence_up_button.setFont(button_font)

        # Down button for occurrence
        self.occurrence_down_button = QPushButton("▼")
        self.occurrence_down_button.setFixedSize(14, 9)  # Match FWL Aggr button size
        self.occurrence_down_button.setFont(button_font)

        # Add buttons to occurrence button layout
        occurrence_button_layout.addWidget(self.occurrence_up_button)
        occurrence_button_layout.addWidget(self.occurrence_down_button)
        occurrence_button_container.setLayout(occurrence_button_layout)

        # Add input and buttons to occurrence container
        occurrence_input_layout.addWidget(self.occurrence_input)
        occurrence_input_layout.addWidget(occurrence_button_container)
        occurrence_input_container.setLayout(occurrence_input_layout)
        occurrence_input_container.setFixedSize(45, 18)  # Match FWL Aggr container size

        # Connect occurrence button signals
        self.occurrence_up_button.clicked.connect(self._increment_occurrence)
        self.occurrence_down_button.clicked.connect(self._decrement_occurrence)

        # Connect text change to validation and data refresh
        self.occurrence_input.textChanged.connect(self._validate_occurrence_input)
        self.occurrence_input.textChanged.connect(self._on_occurrence_changed)

        # Add to horizontal layout
        occurrence_layout.addWidget(occurrence_label)
        occurrence_layout.addWidget(occurrence_input_container)
        occurrence_layout.addStretch()  # Push to left

        # Add to left layout
        left_layout.addLayout(occurrence_layout)
        left_layout.addStretch()  # Push to top

        left_settings.setLayout(left_layout)

        # Right section (1/2) - unified filter radio buttons
        right_settings = QWidget()
        right_settings.setStyleSheet("background-color: #1e1e1e;")
        right_layout = QVBoxLayout()
        right_layout.setContentsMargins(8, 8, 8, 8)
        right_layout.setSpacing(8)

        # Add Market Condition Filter title
        filter_title = QLabel("Market Condition Filter")
        filter_title.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        filter_title.setStyleSheet("color: #ffffff; margin-bottom: 5px;")
        filter_title.setFixedHeight(25)  # Fixed height for alignment
        right_layout.addWidget(filter_title)

        # Create unified button group for exclusive selection
        self.unified_filter_button_group = QButtonGroup()

        # Create radio buttons
        self.hl_matching_radio = QRadioButton("H/L Matching")
        self.weekday_matching_radio = QRadioButton("Weekday Matching")

        # Set font and styling
        radio_font = QFont("Segoe UI", 9)
        self.hl_matching_radio.setFont(radio_font)
        self.weekday_matching_radio.setFont(radio_font)

        # Style radio buttons for dark theme
        filter_radio_style = """
            QRadioButton {
                color: #ffffff;
                border: none;
                outline: none;
                spacing: 3px;
            }
            QRadioButton::indicator {
                width: 12px;
                height: 12px;
            }
            QRadioButton::indicator:unchecked {
                border: 1px solid #555555;
                border-radius: 6px;
                background-color: #1e1e1e;
            }
            QRadioButton::indicator:checked {
                border: 1px solid #ffffff;
                border-radius: 6px;
                background-color: #555555;
            }
        """

        self.hl_matching_radio.setStyleSheet(filter_radio_style)
        self.weekday_matching_radio.setStyleSheet(filter_radio_style)

        # Set H/L Matching as default based on unified state
        if self._unified_filter_type == 0:
            self.hl_matching_radio.setChecked(True)
        elif self._unified_filter_type == 1:
            self.weekday_matching_radio.setChecked(True)

        # Add buttons to group
        self.unified_filter_button_group.addButton(self.hl_matching_radio, 0)
        self.unified_filter_button_group.addButton(self.weekday_matching_radio, 1)

        # Connect to unified filter function
        self.hl_matching_radio.toggled.connect(self.on_unified_filter_changed)
        self.weekday_matching_radio.toggled.connect(self.on_unified_filter_changed)

        # Add this button group to the global list for synchronization
        VolatilityStatisticsTab._all_filter_button_groups.append(self.unified_filter_button_group)

        # Add radio buttons to right layout
        right_layout.addWidget(self.hl_matching_radio)
        right_layout.addWidget(self.weekday_matching_radio)

        # Add some spacing before Backtesting section
        right_layout.addSpacing(10)

        # Add Backtesting title
        backtesting_title = QLabel("Backtesting")
        backtesting_title.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        backtesting_title.setStyleSheet("color: #ffffff; margin-bottom: 5px;")
        backtesting_title.setFixedHeight(25)  # Fixed height for alignment
        right_layout.addWidget(backtesting_title)

        # Create Historical Data button
        self.historical_data_button = QPushButton("Historical Data")
        self.historical_data_button.setFixedHeight(30)
        self.historical_data_button.setFont(QFont("Segoe UI", 9))
        self.historical_data_button.setStyleSheet("""
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                text-align: center;
                padding: 3px 8px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
        """)

        # Connect Historical Data button to handler
        self.historical_data_button.clicked.connect(self._on_historical_data_button_clicked)
        right_layout.addWidget(self.historical_data_button)

        # Create Back to Current Data button
        self.back_to_current_button = QPushButton("Back to Current Data")
        self.back_to_current_button.setFixedHeight(30)
        self.back_to_current_button.setFont(QFont("Segoe UI", 9))
        self.back_to_current_button.setStyleSheet("""
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #000000;
                text-align: center;
                padding: 3px 8px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #1e1e1e;
                border: 1px solid #000000;
            }
            QPushButton:pressed {
                background-color: #1a1a1a;
                border: 1px solid #000000;
            }
            QPushButton:disabled {
                background-color: #2b2b2b;
                color: #555555;
                border: 1px solid #555555;
            }
        """)

        # Connect Back to Current Data button to handler
        self.back_to_current_button.clicked.connect(self._reset_to_current_data)
        # Initially disable the button since we're not viewing historical data
        self.back_to_current_button.setEnabled(False)
        right_layout.addWidget(self.back_to_current_button)

        right_layout.addStretch()  # Push buttons to top

        right_settings.setLayout(right_layout)

        # Add widgets to splitter
        settings_splitter.addWidget(left_settings)
        settings_splitter.addWidget(right_settings)

        # Set equal sizes for 1/2 split - use larger numbers for better precision
        settings_splitter.setSizes([1000, 1000])
        settings_splitter.setStretchFactor(0, 1)
        settings_splitter.setStretchFactor(1, 1)

        # Disable handle movement for fixed split
        settings_handle = settings_splitter.handle(1)
        settings_handle.setEnabled(False)

        settings_layout.addWidget(settings_splitter)

        # Add Zones section within settings content
        zones_container = QWidget()
        zones_container.setStyleSheet("background-color: #1e1e1e; margin-top: 10px;")
        zones_layout = QVBoxLayout()
        zones_layout.setContentsMargins(8, 8, 8, 8)
        zones_layout.setSpacing(8)

        # Add Zones title
        zones_title = QLabel("Zones")
        zones_title.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        zones_title.setStyleSheet("color: #ffffff; margin-bottom: 8px; padding: 4px 0px;")
        zones_title.adjustSize()  # Auto-size to fit text content
        zones_layout.addWidget(zones_title)

        # Create calculate zones button
        self.calculate_zones_button = QPushButton("Intraday Zones")
        self.calculate_zones_button.setFixedHeight(30)
        self.calculate_zones_button.setFont(QFont("Segoe UI", 9))

        # Style the calculate zones button to match other controls in settings
        self.calculate_zones_button.setStyleSheet("""
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                text-align: center;
                padding: 3px 8px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
        """)

        # Connect calculate zones button to handler
        self.calculate_zones_button.clicked.connect(self._on_calculate_zones_button_clicked)
        zones_layout.addWidget(self.calculate_zones_button)

        # Create pivot zone button
        self.pivot_zone_button = QPushButton("Pivot Zone")
        self.pivot_zone_button.setFixedHeight(30)
        self.pivot_zone_button.setFont(QFont("Segoe UI", 9))

        # Style the pivot zone button to match other controls in settings
        self.pivot_zone_button.setStyleSheet("""
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                text-align: center;
                padding: 3px 8px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
        """)

        # Connect pivot zone button to handler
        self.pivot_zone_button.clicked.connect(self._on_pivot_zone_button_clicked)
        zones_layout.addWidget(self.pivot_zone_button)

        # Create weekly zones button
        self.weekly_zones_button = QPushButton("Weekly Zones")
        self.weekly_zones_button.setFixedHeight(30)
        self.weekly_zones_button.setFont(QFont("Segoe UI", 9))

        # Style the weekly zones button to match other controls in settings
        self.weekly_zones_button.setStyleSheet("""
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                text-align: center;
                padding: 3px 8px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
        """)

        # Connect weekly zones button to handler
        self.weekly_zones_button.clicked.connect(self._on_weekly_zones_button_clicked)
        zones_layout.addWidget(self.weekly_zones_button)

        # Create zones viewer button
        self.zones_viewer_button = QPushButton("View Zone Prices")
        self.zones_viewer_button.setFixedHeight(30)
        self.zones_viewer_button.setFont(QFont("Segoe UI", 9))

        # Style the zones viewer button to match other controls in settings
        self.zones_viewer_button.setStyleSheet("""
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                text-align: center;
                padding: 3px 8px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
        """)

        # Connect zones viewer button to handler
        self.zones_viewer_button.clicked.connect(self._on_zones_viewer_button_clicked)
        zones_layout.addWidget(self.zones_viewer_button)

        # Create pivot zone viewer button
        self.pivot_zone_viewer_button = QPushButton("View Pivot Zone Prices")
        self.pivot_zone_viewer_button.setFixedHeight(30)
        self.pivot_zone_viewer_button.setFont(QFont("Segoe UI", 9))

        # Style the pivot zone viewer button to match other controls in settings
        self.pivot_zone_viewer_button.setStyleSheet("""
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                text-align: center;
                padding: 3px 8px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
        """)

        # Connect pivot zone viewer button to handler
        self.pivot_zone_viewer_button.clicked.connect(self._on_pivot_zone_viewer_button_clicked)
        zones_layout.addWidget(self.pivot_zone_viewer_button)

        # Create weekly zones viewer button
        self.weekly_zones_viewer_button = QPushButton("View Weekly Zone Prices")
        self.weekly_zones_viewer_button.setFixedHeight(30)
        self.weekly_zones_viewer_button.setFont(QFont("Segoe UI", 9))

        # Style the weekly zones viewer button to match other controls in settings
        self.weekly_zones_viewer_button.setStyleSheet("""
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                text-align: center;
                padding: 3px 8px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
        """)

        # Connect weekly zones viewer button to handler
        self.weekly_zones_viewer_button.clicked.connect(self._on_weekly_zones_viewer_button_clicked)
        zones_layout.addWidget(self.weekly_zones_viewer_button)
        zones_layout.addStretch()  # Push content to top

        zones_container.setLayout(zones_layout)
        settings_layout.addWidget(zones_container)

        self.unified_settings_content.setLayout(settings_layout)

        # Add to class lists for shared state management
        self._all_settings_buttons.append(self.unified_settings_button)
        self._all_settings_content.append(self.unified_settings_content)

        # Connect button click to toggle function
        self.unified_settings_button.clicked.connect(self.toggle_settings)

        # Add widgets to layout
        layout.addWidget(self.unified_settings_button)
        layout.addWidget(self.unified_settings_content)

        # Add Statistics section below Settings (NOT part of collapsible Settings content)
        statistics_title = QLabel("Statistics")
        statistics_title.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        statistics_title.setStyleSheet("color: #ffffff; margin-bottom: 8px; margin-top: 5px; padding: 2px;")
        statistics_title.setMinimumHeight(25)  # Reduce height to move content up
        layout.addWidget(statistics_title)

        # Create main statistics container
        statistics_container = QWidget()
        statistics_container.setStyleSheet("QWidget { border: none; background-color: transparent; }")
        statistics_container_layout = QVBoxLayout()
        statistics_container_layout.setContentsMargins(0, 0, 0, 10)  # Add bottom margin like charts
        statistics_container_layout.setSpacing(10)

        # Create single scrollable area for both volatility and projected statistics
        from PyQt6.QtWidgets import QScrollArea
        combined_scroll_area = QScrollArea()
        combined_scroll_area.setWidgetResizable(True)
        combined_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        combined_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        combined_scroll_area.setStyleSheet("QScrollArea { border: none; background-color: transparent; }")

        # Widget to hold all statistics (both volatility and projected)
        self.combined_statistics_widget = QWidget()
        self.combined_statistics_widget.setStyleSheet("QWidget { border: none; background-color: transparent; }")
        self.combined_statistics_layout = QVBoxLayout()
        self.combined_statistics_layout.setContentsMargins(2, 2, 2, 2)
        self.combined_statistics_layout.setSpacing(1)
        self.combined_statistics_widget.setLayout(self.combined_statistics_layout)

        combined_scroll_area.setWidget(self.combined_statistics_widget)
        statistics_container_layout.addWidget(combined_scroll_area, 1)  # Give scroll area stretch factor to expand

        statistics_container.setLayout(statistics_container_layout)
        layout.addWidget(statistics_container, 1)  # Give it stretch factor to fill remaining space

        layout.addStretch()  # Push content to top

        settings_widget.setLayout(layout)

        # Add settings widget to container with top margin
        container_layout.addWidget(settings_widget)

        return container_widget



    def toggle_settings(self):
        """Toggle the visibility of unified settings content."""
        # Toggle the class variable
        self._settings_expanded = not self._settings_expanded

        # Update unified settings button and content
        if hasattr(self, 'unified_settings_button'):
            if self._settings_expanded:
                self.unified_settings_button.setText("▼ Settings")  # Expanded
            else:
                self.unified_settings_button.setText("▶ Settings")  # Collapsed
            self.unified_settings_button.setFont(QFont("Segoe UI", 10))

        if hasattr(self, 'unified_settings_content'):
            self.unified_settings_content.setVisible(self._settings_expanded)

    def on_unified_filter_changed(self):
        """Handle unified filter changes that affect all subtabs."""
        if not hasattr(self, 'unified_filter_button_group'):
            return

        # Get the checked button
        checked_button = self.unified_filter_button_group.checkedButton()
        if not checked_button:
            new_filter_type = -1  # No filter
        else:
            # Get the button ID (0 = H/L Matching, 1 = Weekday Matching)
            new_filter_type = self.unified_filter_button_group.id(checked_button)

        # Update unified filter state
        VolatilityStatisticsTab._unified_filter_type = new_filter_type

        # Synchronize all filter button groups across all instances
        self.synchronize_all_filter_states(new_filter_type)

        # Apply filter to all volatility tab instances
        self.apply_unified_filter_to_all_tabs()

    @classmethod
    def synchronize_all_filter_states(cls, filter_type):
        """Synchronize filter states across all volatility tab instances."""
        for button_group in cls._all_filter_button_groups:
            # Temporarily disconnect signals to avoid recursion
            for button in button_group.buttons():
                button.blockSignals(True)

            # Update button states
            if filter_type == 0:  # H/L Matching
                button_group.button(0).setChecked(True)
            elif filter_type == 1:  # Weekday Matching
                button_group.button(1).setChecked(True)
            else:  # No filter
                for button in button_group.buttons():
                    button.setChecked(False)

            # Re-enable signals
            for button in button_group.buttons():
                button.blockSignals(False)

    @classmethod
    def apply_unified_filter_to_all_tabs(cls):
        """Apply the unified filter to all volatility tab instances."""
        for tab_instance in cls._all_volatility_tabs:
            if hasattr(tab_instance, '_original_high_data') and hasattr(tab_instance, '_original_low_data'):
                tab_instance.apply_unified_filter()

    def apply_unified_filter(self):
        """Apply the unified filter using backend processing - affects all subtabs."""
        if not self._original_high_data or not self._original_low_data:
            return

        if not self.data_service:
            return

        try:
            # CRITICAL: Ensure historical close price is applied before any backend calculations
            if self.viewing_historical:
                self._ensure_historical_close_price_is_applied()

            # Apply historical filter if viewing historical data
            high_data_for_calculation = self._original_high_data
            low_data_for_calculation = self._original_low_data

            if self.viewing_historical and hasattr(self, 'historical_index') and self.historical_index is not None:
                # Apply historical filter: use only data up to historical_index (inclusive)
                cutoff_idx = self.historical_index
                print(f"Applying historical filter: using data from 0 to {cutoff_idx} (inclusive)")

                high_data_for_calculation = []
                for i, high_entry in enumerate(self._original_high_data):
                    if i <= cutoff_idx:
                        high_data_for_calculation.append(high_entry)

                low_data_for_calculation = []
                for i, low_entry in enumerate(self._original_low_data):
                    if i <= cutoff_idx:
                        low_data_for_calculation.append(low_entry)

                print(f"Historical filter applied: using {len(high_data_for_calculation)} high and {len(low_data_for_calculation)} low entries")

            # Request filtered data from backend using unified filter state
            filter_result = self.data_service.apply_volatility_statistics_filter(
                high_data_for_calculation, low_data_for_calculation, self._unified_filter_type
            )

            # Get filtered data from backend
            filtered_high = filter_result.get("volatility_filtered_high_data", [])
            filtered_low = filter_result.get("volatility_filtered_low_data", [])

            # Apply occurrence limit if set
            occurrence_count = self.get_occurrence_count()
            if occurrence_count > 0:
                # Limit to latest N occurrences (chronologically)
                # The data is already sorted chronologically, so take the last N items
                if len(filtered_high) > occurrence_count:
                    filtered_high = filtered_high[-occurrence_count:]
                if len(filtered_low) > occurrence_count:
                    filtered_low = filtered_low[-occurrence_count:]

            # Update both high and low tables if they exist
            if hasattr(self, 'high_table_model'):
                self.high_table_model.beginResetModel()
                self.high_table_model._data = filtered_high
                self.high_table_model.endResetModel()

            if hasattr(self, 'low_table_model'):
                self.low_table_model.beginResetModel()
                self.low_table_model._data = filtered_low
                self.low_table_model.endResetModel()

            # Update charts with the same filtered data and market data
            market_data = getattr(self, '_market_data', None)
            self.update_charts_with_filtered_data(filtered_high, filtered_low, market_data)

        except Exception as e:
            print(f"Error applying unified volatility statistics filter: {e}")
            # Fallback: show unfiltered data
            if hasattr(self, 'high_table_model'):
                self.high_table_model.beginResetModel()
                self.high_table_model._data = self._original_high_data
                self.high_table_model.endResetModel()

            if hasattr(self, 'low_table_model'):
                self.low_table_model.beginResetModel()
                self.low_table_model._data = self._original_low_data
                self.low_table_model.endResetModel()

    def switch_chart(self, button):
        """Switch between different chart types based on radio button selection."""
        if hasattr(self, 'chart_stack'):
            button_id = self.button_group.id(button)
            self.chart_stack.setCurrentIndex(button_id)

            # Show/hide options header based on chart selection
            if hasattr(self, 'options_header'):
                # Show options header only for density chart (index 1)
                self.options_header.setVisible(button_id == 1)

    def create_header_with_radio_buttons(self):
        """Create small header with 3 central radio buttons."""
        header_widget = QWidget()
        header_widget.setFixedHeight(30)
        header_widget.setStyleSheet("background-color: #2b2b2b;")
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(5, 2, 5, 2)
        header_layout.setSpacing(10)

        # Create button group for exclusive selection
        self.button_group = QButtonGroup()

        # Create radio buttons with smaller font
        volatility_radio = QRadioButton("Volatility Graph")
        density_radio = QRadioButton("Density Graph")
        fwl_radio = QRadioButton("FWL Odds")

        # Set font size using PyQt6
        font = QFont("Segoe UI", 10)
        volatility_radio.setFont(font)
        density_radio.setFont(font)
        fwl_radio.setFont(font)

        # Style radio buttons to match dark theme (minimal CSS for radio buttons only)
        radio_style = """
            QRadioButton {
                color: #ffffff;
                border: none;
                outline: none;
            }
            QRadioButton::indicator {
                width: 14px;
                height: 14px;
            }
            QRadioButton::indicator:unchecked {
                border: 1px solid #555555;
                border-radius: 7px;
                background-color: #1e1e1e;
            }
            QRadioButton::indicator:checked {
                border: 1px solid #ffffff;
                border-radius: 7px;
                background-color: #555555;
            }
        """

        volatility_radio.setStyleSheet(radio_style)
        density_radio.setStyleSheet(radio_style)
        fwl_radio.setStyleSheet(radio_style)

        # Set default selection
        volatility_radio.setChecked(True)

        # Add buttons to group
        self.button_group.addButton(volatility_radio, 0)
        self.button_group.addButton(density_radio, 1)
        self.button_group.addButton(fwl_radio, 2)

        # Connect radio buttons to chart switching
        self.button_group.buttonClicked.connect(self.switch_chart)

        # Center the buttons
        header_layout.addStretch()
        header_layout.addWidget(volatility_radio)
        header_layout.addWidget(density_radio)
        header_layout.addWidget(fwl_radio)
        header_layout.addStretch()

        header_widget.setLayout(header_layout)
        return header_widget

    def create_options_header(self):
        """Create options header with bid/ask radio buttons and expiration dropdown."""
        header_widget = QWidget()
        header_widget.setFixedHeight(30)
        header_widget.setStyleSheet("background-color: #2b2b2b;")
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(5, 2, 5, 2)
        header_layout.setSpacing(15)

        # Create price type radio buttons
        self.price_type_group = QButtonGroup()

        bid_radio = QRadioButton("Bid Prices")
        ask_radio = QRadioButton("Ask Prices")

        # Set font size
        font = QFont("Segoe UI", 9)
        bid_radio.setFont(font)
        ask_radio.setFont(font)

        # Style radio buttons to match dark theme
        radio_style = """
            QRadioButton {
                color: #ffffff;
                border: none;
                outline: none;
            }
            QRadioButton::indicator {
                width: 12px;
                height: 12px;
            }
            QRadioButton::indicator:unchecked {
                border: 1px solid #555555;
                border-radius: 6px;
                background-color: #1e1e1e;
            }
            QRadioButton::indicator:checked {
                border: 1px solid #ffffff;
                border-radius: 6px;
                background-color: #555555;
            }
        """

        bid_radio.setStyleSheet(radio_style)
        ask_radio.setStyleSheet(radio_style)

        # Set default to ask prices
        ask_radio.setChecked(True)

        # Add buttons to group
        self.price_type_group.addButton(bid_radio, 0)  # 0 = bid
        self.price_type_group.addButton(ask_radio, 1)  # 1 = ask

        # Connect to update function
        self.price_type_group.buttonClicked.connect(self.on_price_type_changed)

        # Create expiration dropdown
        # Create expiration dropdown (PyQt6 native only, no CSS)
        self.expiry_dropdown = QComboBox()
        self.expiry_dropdown.setFixedWidth(120)
        self.expiry_dropdown.setFont(QFont("Segoe UI", 9))

        # Connect dropdown to update function
        self.expiry_dropdown.currentTextChanged.connect(self.on_expiry_changed)

        # Create label for dropdown
        expiry_label = QLabel("Expiry:")
        expiry_label.setFont(QFont("Segoe UI", 9))
        expiry_label.setStyleSheet("color: #ffffff; border: none;")

        # Layout the header
        header_layout.addStretch()
        header_layout.addWidget(bid_radio)
        header_layout.addWidget(ask_radio)
        header_layout.addWidget(QLabel("|"))  # Separator
        header_layout.addWidget(expiry_label)
        header_layout.addWidget(self.expiry_dropdown)
        header_layout.addStretch()

        header_widget.setLayout(header_layout)
        return header_widget

    def on_price_type_changed(self, button):
        """Handle price type radio button changes."""
        import logging
        logger = logging.getLogger(__name__)

        price_type = 'bid' if self.price_type_group.id(button) == 0 else 'ask'
        logger.info(f"Price type changed to {price_type}")

        # Update density chart
        if hasattr(self, 'density_chart'):
            self.density_chart.set_price_type(price_type)
            # Refresh the chart with current data
            if hasattr(self, '_current_filtered_high_data') and hasattr(self, '_current_filtered_low_data'):
                logger.info(f"Updating density chart with {price_type} prices")
                self.density_chart.update_data(
                    self._current_filtered_high_data,
                    self._current_filtered_low_data,
                    self._current_market_data
                )

        # Update FWL odds chart (check if it has set_price_type method)
        if hasattr(self, 'fwl_odds_chart') and hasattr(self.fwl_odds_chart, 'set_price_type'):
            self.fwl_odds_chart.set_price_type(price_type)
            # Refresh the chart with current data
            if hasattr(self, '_current_filtered_high_data') and hasattr(self, '_current_filtered_low_data'):
                logger.info(f"Updating FWL odds chart with {price_type} prices")
                self.fwl_odds_chart.update_data(
                    self._current_filtered_high_data,
                    self._current_filtered_low_data,
                    self._current_market_data
                )
        else:
            logger.debug("FWL odds chart does not support price type changes")

    def on_expiry_changed(self, expiry_text):
        """Handle expiration dropdown changes."""
        if hasattr(self, 'density_chart') and expiry_text:
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"Expiry changed to {expiry_text}")
            self.density_chart.set_selected_expiry(expiry_text)

            # Refresh the chart with current data and new expiry
            if hasattr(self, '_current_filtered_high_data') and hasattr(self, '_current_filtered_low_data'):
                # Fetch new options data for the selected expiry
                if hasattr(self, '_current_market_data') and self._current_market_data:
                    # Update the density chart with new expiry
                    self.density_chart.update_data(
                        self._current_filtered_high_data,
                        self._current_filtered_low_data,
                        self._current_market_data
                    )

    def create_data_header(self):
        """Create header with 'Projected Data' title."""
        header_widget = QWidget()
        header_widget.setFixedHeight(30)
        header_widget.setStyleSheet("background-color: #2b2b2b;")
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(5, 2, 5, 2)

        # Create title label
        title_label = QLabel("Projected Data")
        title_label.setFont(QFont("Segoe UI", 12))
        title_label.setStyleSheet("color: #ffffff; border: none; outline: none;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Center the title
        header_layout.addStretch()
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        header_widget.setLayout(header_layout)
        return header_widget

    def create_chart_tab(self):
        """Create the Chart tab widget - full width since settings are now unified."""
        chart_widget = QWidget()
        chart_widget.setStyleSheet("border: 2px solid #2b2b2b; background-color: #1e1e1e;")
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Add header with radio buttons
        header = self.create_header_with_radio_buttons()
        layout.addWidget(header)

        # Add options header (only visible when density chart is selected)
        self.options_header = self.create_options_header()
        layout.addWidget(self.options_header)
        self.options_header.setVisible(False)  # Initially hidden

        # Add main content area with stacked widget for charts
        content_area = QWidget()
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(5, 5, 5, 5)
        content_layout.setSpacing(0)

        # Create stacked widget to hold different charts
        self.chart_stack = QStackedWidget()

        # Create and add the three chart types
        self.volatility_chart = VolatilityChart()
        self.density_chart = DensityChart()
        self.fwl_odds_chart = FWLOddsChart()

        self.chart_stack.addWidget(self.volatility_chart)  # Index 0
        self.chart_stack.addWidget(self.density_chart)     # Index 1
        self.chart_stack.addWidget(self.fwl_odds_chart)    # Index 2

        # Set default to volatility chart
        self.chart_stack.setCurrentIndex(0)

        content_layout.addWidget(self.chart_stack)
        content_area.setLayout(content_layout)
        layout.addWidget(content_area)

        chart_widget.setLayout(layout)
        return chart_widget

    def create_data_tab(self):
        """Create the Data tab widget - full width since settings are now unified."""
        data_widget = QWidget()
        data_widget.setStyleSheet("border: 2px solid #2b2b2b; background-color: #1e1e1e;")
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Add header with "Projected Data" title
        header = self.create_data_header()
        layout.addWidget(header)

        # Add main content area with vertical split for high/low data
        content_area = QWidget()
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # Create vertical splitter for high/low data split
        data_splitter = QSplitter(Qt.Orientation.Vertical)
        data_splitter.setChildrenCollapsible(False)
        data_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #2b2b2b;
                height: 2px;
            }
        """)

        # Top section - Projected High Data
        top_widget = QWidget()
        top_widget.setStyleSheet("border: 1px solid #2b2b2b; background-color: #1e1e1e;")
        top_layout = QVBoxLayout()
        top_layout.setContentsMargins(10, 5, 10, 5)
        top_layout.setSpacing(5)

        # Title for high data
        high_title = QLabel("Projected High Data")
        high_title.setFont(QFont("Segoe UI", 12))
        high_title.setStyleSheet("color: #ffffff; font-weight: bold; margin-bottom: 5px;")
        high_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        top_layout.addWidget(high_title)

        # Create high data table
        self.high_table_model = MarketDataTableModel()
        self.high_table_view = QTableView()
        self.high_table_view.setModel(self.high_table_model)
        self.setup_high_table()
        top_layout.addWidget(self.high_table_view)

        top_widget.setLayout(top_layout)

        # Bottom section - Projected Low Data
        bottom_widget = QWidget()
        bottom_widget.setStyleSheet("border: 1px solid #2b2b2b; background-color: #1e1e1e;")
        bottom_layout = QVBoxLayout()
        bottom_layout.setContentsMargins(10, 5, 10, 5)
        bottom_layout.setSpacing(5)

        # Title for low data
        low_title = QLabel("Projected Low Data")
        low_title.setFont(QFont("Segoe UI", 12))
        low_title.setStyleSheet("color: #ffffff; font-weight: bold; margin-bottom: 5px;")
        low_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        bottom_layout.addWidget(low_title)

        # Create low data table
        self.low_table_model = MarketDataTableModel()
        self.low_table_view = QTableView()
        self.low_table_view.setModel(self.low_table_model)
        self.setup_low_table()
        bottom_layout.addWidget(self.low_table_view)

        bottom_widget.setLayout(bottom_layout)

        # Add widgets to splitter
        data_splitter.addWidget(top_widget)
        data_splitter.addWidget(bottom_widget)

        # Set equal sizes for 1/2 split
        data_splitter.setSizes([500, 500])
        data_splitter.setStretchFactor(0, 1)
        data_splitter.setStretchFactor(1, 1)

        # Disable handle movement for fixed split
        handle = data_splitter.handle(1)
        handle.setEnabled(False)

        content_layout.addWidget(data_splitter)
        content_area.setLayout(content_layout)
        layout.addWidget(content_area)

        data_widget.setLayout(layout)
        return data_widget

    def setup_high_table(self):
        """Configure high data table with specific columns."""
        # Set custom headers for high data (Index added as first column)
        # Insert new "Updated" column after Category and "Odds" column after Projected High
        high_headers = [
            "Index", "Weekday", "Category", "Updated",
            "$Change High", "%Change High", "$Projected High Change", "Projected High", "Open", "Close", "Odds"
        ]
        self.high_table_model._headers = high_headers
        self.high_table_model._visible_columns = len(high_headers)

        # Override the data method to insert the "Updated" column (index 3), "Open" column (index 8), "Close" column (index 9), and "Odds" column (index 10)
        original_data_method = self.high_table_model.data
        def custom_high_data(index, role=Qt.ItemDataRole.DisplayRole):
            if role == Qt.ItemDataRole.DisplayRole:
                col = index.column()
                updated_col_index = 3  # "Updated" column position
                open_col_index = 8     # "Open" column position
                close_col_index = 9    # "Close" column position
                odds_col_index = 10    # "Odds" column position

                # Show PH:X in Updated column for high table
                if col == updated_col_index:
                    return self._get_fwl_aggr_display(index.row(), "high")

                # Show open ratio in Open column for high table
                if col == open_col_index:
                    return self._get_open_ratio_display(index.row(), "high")

                # Show close ratio in Close column for high table
                if col == close_col_index:
                    return self._get_close_ratio_display(index.row(), "high")

                # Show odds percentage in Odds column for high table
                if col == odds_col_index:
                    return self._calculate_odds_percentage(index.row(), "high")

                # Map display columns to source row_data indices
                if col < updated_col_index:
                    source_col = col
                elif col < open_col_index:
                    source_col = col - 1  # Account for inserted Updated column
                elif col < close_col_index:
                    source_col = col - 2  # Account for Updated and Open columns
                elif col < odds_col_index:
                    source_col = col - 3  # Account for Updated, Open, and Close columns
                else:
                    source_col = col - 4  # Account for all inserted columns

                row_data = self.high_table_model._data[index.row()] if index.row() < len(self.high_table_model._data) else None
                if row_data and 0 <= source_col < len(row_data):
                    value = row_data[source_col]
                    # Format Projected High (source index 6) to 2 decimals
                    if source_col == 6:
                        try:
                            if value != "" and value is not None:
                                return f"{float(value):.2f}"
                        except (ValueError, TypeError):
                            pass
                    return str(value)
                return ""
            elif role == Qt.ItemDataRole.ForegroundRole:
                # Use same text color as last row
                from PyQt6.QtGui import QColor
                return QColor("#ffffff")
            elif role == Qt.ItemDataRole.BackgroundRole:
                # Use same background color as last row
                from PyQt6.QtGui import QColor
                return QColor("#2b2b2b")
            return original_data_method(index, role)

        self.high_table_model.data = custom_high_data

        # Configure table appearance
        self.setup_table_appearance(self.high_table_view)

    def setup_low_table(self):
        """Configure low data table with specific columns."""
        # Set custom headers for low data (Index added as first column)
        # Insert new "Updated" column after Category and "Odds" column after Projected Low
        low_headers = [
            "Index", "Weekday", "Category", "Updated",
            "$Change Low", "%Change Low", "$Projected Low Change", "Projected Low", "Open", "Close", "Odds"
        ]
        self.low_table_model._headers = low_headers
        self.low_table_model._visible_columns = len(low_headers)

        # Override the data method to insert the "Updated" column (index 3), "Open" column (index 8), "Close" column (index 9), and "Odds" column (index 10)
        original_data_method = self.low_table_model.data
        def custom_low_data(index, role=Qt.ItemDataRole.DisplayRole):
            if role == Qt.ItemDataRole.DisplayRole:
                col = index.column()
                updated_col_index = 3  # "Updated" column position
                open_col_index = 8     # "Open" column position
                close_col_index = 9    # "Close" column position
                odds_col_index = 10    # "Odds" column position

                # Show PL:X in Updated column for low table
                if col == updated_col_index:
                    return self._get_fwl_aggr_display(index.row(), "low")

                # Show open ratio in Open column for low table
                if col == open_col_index:
                    return self._get_open_ratio_display(index.row(), "low")

                # Show close ratio in Close column for low table
                if col == close_col_index:
                    return self._get_close_ratio_display(index.row(), "low")

                # Show odds percentage in Odds column for low table
                if col == odds_col_index:
                    return self._calculate_odds_percentage(index.row(), "low")

                # Map display columns to source row_data indices
                if col < updated_col_index:
                    source_col = col
                elif col < open_col_index:
                    source_col = col - 1  # Account for inserted Updated column
                elif col < close_col_index:
                    source_col = col - 2  # Account for Updated and Open columns
                elif col < odds_col_index:
                    source_col = col - 3  # Account for Updated, Open, and Close columns
                else:
                    source_col = col - 4  # Account for all inserted columns

                row_data = self.low_table_model._data[index.row()] if index.row() < len(self.low_table_model._data) else None
                if row_data and 0 <= source_col < len(row_data):
                    value = row_data[source_col]
                    # Format Projected Low (source index 6) to 2 decimals
                    if source_col == 6:
                        try:
                            if value != "" and value is not None:
                                return f"{float(value):.2f}"
                        except (ValueError, TypeError):
                            pass
                    return str(value)
                return ""
            elif role == Qt.ItemDataRole.ForegroundRole:
                # Use same text color as last row
                from PyQt6.QtGui import QColor
                return QColor("#ffffff")
            elif role == Qt.ItemDataRole.BackgroundRole:
                # Use same background color as last row
                from PyQt6.QtGui import QColor
                return QColor("#2b2b2b")
            return original_data_method(index, role)

        self.low_table_model.data = custom_low_data

        # Configure table appearance
        self.setup_table_appearance(self.low_table_view)

    def setup_table_appearance(self, table_view):
        """Configure table appearance for both high and low tables."""
        # Selection behavior
        table_view.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        table_view.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)

        # Appearance
        table_view.setAlternatingRowColors(True)
        table_view.setShowGrid(True)
        table_view.setGridStyle(Qt.PenStyle.SolidLine)

        # Header configuration for evenly spread columns
        horizontal_header = table_view.horizontalHeader()
        horizontal_header.setStretchLastSection(True)

        # Set all columns to stretch evenly based on the model attached to this view
        model = table_view.model()
        col_count = model.columnCount() if model is not None else 0
        for i in range(col_count):
            horizontal_header.setSectionResizeMode(i, horizontal_header.ResizeMode.Stretch)

        # Vertical header
        vertical_header = table_view.verticalHeader()
        vertical_header.setVisible(True)
        vertical_header.setDefaultSectionSize(20)  # Smaller rows for split view

        # Dark theme styling
        table_view.setStyleSheet("""
            QTableView {
                background-color: #1e1e1e;
                color: #ffffff;
                gridline-color: #555555;
                selection-background-color: #3c3c3c;
                alternate-background-color: #2b2b2b;
            }
            QHeaderView::section {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 2px;
                font-weight: bold;
                font-size: 9px;
            }
        """)

    def setup_projected_table_view(self):
        """Configure projected table view with same settings as data_tab.py."""
        # Selection behavior
        self.projected_table_view.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.projected_table_view.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)

        # Appearance
        self.projected_table_view.setAlternatingRowColors(True)
        self.projected_table_view.setShowGrid(True)
        self.projected_table_view.setGridStyle(Qt.PenStyle.SolidLine)

        # Header configuration for evenly spread columns
        horizontal_header = self.projected_table_view.horizontalHeader()
        horizontal_header.setStretchLastSection(True)

        # Set all columns to stretch evenly
        for i in range(self.projected_table_model.columnCount()):
            horizontal_header.setSectionResizeMode(i, horizontal_header.ResizeMode.Stretch)

        # Vertical header
        vertical_header = self.projected_table_view.verticalHeader()
        vertical_header.setVisible(True)
        vertical_header.setDefaultSectionSize(25)

        # Dark theme styling to match the application
        self.projected_table_view.setStyleSheet("""
            QTableView {
                background-color: #1e1e1e;
                color: #ffffff;
                gridline-color: #555555;
                selection-background-color: #3c3c3c;
                alternate-background-color: #2b2b2b;
            }
            QHeaderView::section {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 4px;
                font-weight: bold;
            }
        """)

    def _calculate_odds_percentage(self, table_row_index, table_type):
        """Calculate FWL odds percentage for a specific row using the same method as FWL Odds chart.

        Args:
            table_row_index: The row index in the volatility table
            table_type: "high" or "low" to determine which data to use
        """
        try:
            # Get all projected values for the current table type
            if table_type == "high":
                table_model = self.high_table_model
                projected_col_index = 6  # Projected High is at source index 6
            else:  # table_type == "low"
                table_model = self.low_table_model
                projected_col_index = 6  # Projected Low is at source index 6

            # Extract all projected values from the table data
            projected_values = []
            for row_data in table_model._data:
                if len(row_data) > projected_col_index:
                    try:
                        projected_str = str(row_data[projected_col_index])
                        if projected_str and projected_str != '' and projected_str != 'None':
                            projected_value = float(projected_str)
                            projected_values.append(projected_value)
                    except (ValueError, TypeError):
                        continue

            if not projected_values or table_row_index >= len(table_model._data):
                return ""

            # Get the current row's projected value
            current_row_data = table_model._data[table_row_index]
            if len(current_row_data) <= projected_col_index:
                return ""

            try:
                current_projected_str = str(current_row_data[projected_col_index])
                if not current_projected_str or current_projected_str == '' or current_projected_str == 'None':
                    return ""
                current_projected = float(current_projected_str)
            except (ValueError, TypeError):
                return ""

            # Calculate odds using the same method as FWL Odds chart
            sorted_values = sorted(projected_values)
            n_values = len(sorted_values)

            if n_values == 1:
                percentage = 25.5  # Single value gets midpoint
            else:
                # Find the rank/position of this value in the sorted list
                value_rank = sorted_values.index(current_projected)

                if table_type == "high":
                    # For highs: highest (last in sorted list) = 1%, lowest (first in sorted list) = 50%
                    # Reverse the rank so highest value gets rank 0
                    reversed_rank = n_values - 1 - value_rank
                    ratio = reversed_rank / (n_values - 1)
                    percentage = 1.0 + (ratio * 49.0)  # 1% to 50%
                else:  # table_type == "low"
                    # For lows: lowest (first in sorted list) = 1%, highest (last in sorted list) = 50%
                    ratio = value_rank / (n_values - 1)
                    percentage = 1.0 + (ratio * 49.0)  # 1% to 50%

            return f"{percentage:.1f}%"

        except Exception as e:
            return ""

    def _get_fwl_aggr_display(self, table_row_index, table_type="both"):
        """Get FWL Aggr display text for Updated column.

        Args:
            table_row_index: The row index in the volatility table
            table_type: "high", "low", or "both" to determine what to display
        """
        try:
            # Get FWL Aggr value from stored market data
            if hasattr(self, '_market_data') and self._market_data:
                fwl_aggr = self._market_data.get('fwl_aggr_value', 1)

                # Get the original data index from the volatility table data
                # The first column (index 0) contains the original row index from main data table
                original_data_index = None
                if table_type == "high" and hasattr(self, 'high_table_model'):
                    if table_row_index < len(self.high_table_model._data):
                        row_data = self.high_table_model._data[table_row_index]
                        if row_data and len(row_data) > 0:
                            original_data_index = row_data[0]  # First column is the original index
                elif table_type == "low" and hasattr(self, 'low_table_model'):
                    if table_row_index < len(self.low_table_model._data):
                        row_data = self.low_table_model._data[table_row_index]
                        if row_data and len(row_data) > 0:
                            original_data_index = row_data[0]  # First column is the original index

                if original_data_index is not None:
                    # Get specific PH/PL values for this original data index from metadata
                    fwl_metadata = self._market_data.get('fwl_aggr_metadata', {})
                    ph = fwl_aggr  # Default value
                    pl = fwl_aggr  # Default value

                    if str(original_data_index) in fwl_metadata:
                        metadata_row = fwl_metadata[str(original_data_index)]
                        ph = metadata_row.get('ph', fwl_aggr)
                        pl = metadata_row.get('pl', fwl_aggr)
                    elif original_data_index in fwl_metadata:
                        metadata_row = fwl_metadata[original_data_index]
                        ph = metadata_row.get('ph', fwl_aggr)
                        pl = metadata_row.get('pl', fwl_aggr)

                    # Return based on table type
                    if table_type == "high":
                        return f"PH:{ph}"
                    elif table_type == "low":
                        return f"PL:{pl}"
                    else:  # both
                        return f"PH:{ph}|PL:{pl}"
            return ""
        except Exception as e:
            print(f"Error getting FWL Aggr display: {e}")
            return ""

    def _get_open_ratio_display(self, table_row_index, table_type="both"):
        """Get open ratio display for Open column.

        Args:
            table_row_index: The row index in the volatility table
            table_type: "high" or "low" to determine which table to use
        """
        try:
            # Get the projected OHLC data from market data
            if hasattr(self, '_market_data') and self._market_data:
                projected_ohlc_rows = self._market_data.get('projected_ohlc_table_rows', [])

                # Get the original data index from the volatility table data
                original_data_index = None
                if table_type == "high" and hasattr(self, 'high_table_model'):
                    if table_row_index < len(self.high_table_model._data):
                        row_data = self.high_table_model._data[table_row_index]
                        if row_data and len(row_data) > 0:
                            original_data_index = row_data[0]  # First column is the original index
                elif table_type == "low" and hasattr(self, 'low_table_model'):
                    if table_row_index < len(self.low_table_model._data):
                        row_data = self.low_table_model._data[table_row_index]
                        if row_data and len(row_data) > 0:
                            original_data_index = row_data[0]  # First column is the original index

                # Get open ratio from projected OHLC data (column index 11)
                if original_data_index is not None and projected_ohlc_rows:
                    try:
                        original_index = int(original_data_index)
                        if 0 <= original_index < len(projected_ohlc_rows):
                            projected_row = projected_ohlc_rows[original_index]
                            if len(projected_row) > 11:  # Open ratio is at index 11
                                open_ratio = projected_row[11]
                                if open_ratio and open_ratio != "":
                                    return str(open_ratio)
                    except (ValueError, IndexError):
                        pass
            return ""
        except Exception as e:
            print(f"Error getting open ratio display: {e}")
            return ""

    def _get_close_ratio_display(self, table_row_index, table_type="both"):
        """Get close ratio display for Close column.

        Args:
            table_row_index: The row index in the volatility table
            table_type: "high" or "low" to determine which table to use
        """
        try:
            # Get the projected OHLC data from market data
            if hasattr(self, '_market_data') and self._market_data:
                projected_ohlc_rows = self._market_data.get('projected_ohlc_table_rows', [])

                # Get the original data index from the volatility table data
                original_data_index = None
                if table_type == "high" and hasattr(self, 'high_table_model'):
                    if table_row_index < len(self.high_table_model._data):
                        row_data = self.high_table_model._data[table_row_index]
                        if row_data and len(row_data) > 0:
                            original_data_index = row_data[0]  # First column is the original index
                elif table_type == "low" and hasattr(self, 'low_table_model'):
                    if table_row_index < len(self.low_table_model._data):
                        row_data = self.low_table_model._data[table_row_index]
                        if row_data and len(row_data) > 0:
                            original_data_index = row_data[0]  # First column is the original index

                # Get close ratio from projected OHLC data (column index 12)
                # The projected OHLC structure: Date, Weekday, Category, $Change High, $Change Low,
                # %Change High, %Change Low, $Projected High Change, $Projected Low Change,
                # Projected High, Projected Low, Open ratio, Close ratio, bg_color, fg_color
                if original_data_index is not None and projected_ohlc_rows:
                    try:
                        original_index = int(original_data_index)
                        if 0 <= original_index < len(projected_ohlc_rows):
                            projected_row = projected_ohlc_rows[original_index]
                            if len(projected_row) > 12:  # Close ratio is at index 12
                                close_ratio = projected_row[12]
                                if close_ratio and close_ratio != "":
                                    return str(close_ratio)
                    except (ValueError, IndexError):
                        pass
            return ""
        except Exception as e:
            print(f"Error getting close ratio display: {e}")
            return ""

    def update_data(self, market_data: Dict[str, Any]):
        """Update both high and low data tables with pre-computed data from backend."""
        if not hasattr(self, 'high_table_model') or not hasattr(self, 'low_table_model'):
            return

        # Get pre-computed volatility statistics data from backend
        high_data = market_data.get("volatility_high_data", [])
        low_data = market_data.get("volatility_low_data", [])
        filtered_high_data = market_data.get("volatility_filtered_high_data", [])
        filtered_low_data = market_data.get("volatility_filtered_low_data", [])

        if not high_data or not low_data:
            return

        # Store original data for unified filtering across all instances
        self._original_high_data = high_data
        self._original_low_data = low_data
        self._market_data = market_data  # Store market data for chart updates
        self._current_market_data = market_data  # Store for options callbacks

        # Cache base (current-FWL) and precomputed 5-FWL datasets for local switching without refetch
        try:
            self._base_original_high_data = list(high_data)
            self._base_original_low_data = list(low_data)
            self._precomputed_high_data_5fwl = list(market_data.get('volatility_high_data_5fwl', high_data))
            self._precomputed_low_data_5fwl = list(market_data.get('volatility_low_data_5fwl', low_data))
            self._last_fwl_local_mode = 'base'
        except Exception:
            pass

        # Update expiry dropdown if density chart is available
        self.update_expiry_dropdown(market_data)

        # Update tables with filtered data (initially H/L matching filtered)
        self.high_table_model.beginResetModel()
        self.high_table_model._data = filtered_high_data
        self.high_table_model.endResetModel()

        self.low_table_model.beginResetModel()
        self.low_table_model._data = filtered_low_data
        self.low_table_model.endResetModel()

        # Update charts with filtered data and market data for close price
        self.update_charts_with_filtered_data(filtered_high_data, filtered_low_data, market_data)

        # Update bias statistics (always shown regardless of mode)
        self.update_bias_statistics_standalone()

        # Apply unified filter to ensure consistency across all tabs
        self.apply_unified_filter()

    def update_bias_statistics_standalone(self):
        """Update bias statistics independently - called from main update_data method."""
        try:
            # Check if we have market data
            if not hasattr(self, '_market_data') or not self._market_data:
                return

            market_data = self._market_data
            projected_ohlc_rows = market_data.get('projected_ohlc_table_rows', [])

            if not projected_ohlc_rows:
                return

            # Apply historical filter to projected OHLC data if viewing historical
            filtered_projected_ohlc_rows = projected_ohlc_rows
            if self.viewing_historical and hasattr(self, 'projected_ohlc_historical_cutoff'):
                cutoff_idx = self.projected_ohlc_historical_cutoff
                filtered_projected_ohlc_rows = projected_ohlc_rows[:cutoff_idx + 1]
                print(f"Applying historical filter to bias calculations: using {len(filtered_projected_ohlc_rows)} out of {len(projected_ohlc_rows)} OHLC rows")

            # Calculate weekday bias statistics
            weekday_bias = self.density_chart._calculate_weekday_bias(filtered_projected_ohlc_rows)

            # Calculate H/L matching bias statistics
            hl_bias = self.density_chart._calculate_hl_matching_bias(filtered_projected_ohlc_rows, market_data)

            # Find the combined statistics layout
            if hasattr(self.density_chart, 'combined_statistics_layout'):
                # Clear existing bias statistics (but not all statistics)
                self.density_chart._clear_bias_statistics()

                # Add bias statistics title
                bias_stats_title = QLabel("Bias")
                bias_stats_title.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
                bias_stats_title.setStyleSheet("color: #ffffff; margin-bottom: 5px; padding: 2px;")
                bias_stats_title.setObjectName("bias_title")  # For identification
                self.density_chart.combined_statistics_layout.insertWidget(0, bias_stats_title)  # Insert at top

                # Add bias column headers
                self.density_chart._add_bias_headers()

                # Add weekday bias statistics
                if weekday_bias:
                    for label, data in weekday_bias.items():
                        if isinstance(data, dict):
                            self.density_chart._add_bias_row_standalone(label, data["percentage"], data["count"])
                        else:
                            # Fallback for old format
                            self.density_chart._add_bias_row_standalone(label, data, "")

                # Add H/L matching bias statistics
                if hl_bias:
                    for label, data in hl_bias.items():
                        if isinstance(data, dict):
                            self.density_chart._add_bias_row_standalone(label, data["percentage"], data["count"])
                        else:
                            # Fallback for old format
                            self.density_chart._add_bias_row_standalone(label, data, "")

        except Exception as e:
            print(f"Error updating standalone bias statistics: {e}")

    def update_expiry_dropdown(self, market_data):
        """Update the expiry dropdown with available expiration dates."""
        try:
            if not hasattr(self, 'expiry_dropdown') or not hasattr(self, 'density_chart'):
                return

            # Fetch options data to get available expiration dates
            ticker = market_data.get("ticker", "") if market_data else ""
            options_data = self.data_service.get_options_data(ticker) if ticker and self.data_service else None

            if options_data and 'expiry_dates' in options_data:
                expiry_dates = options_data['expiry_dates']

                # Clear and populate dropdown
                self.expiry_dropdown.clear()
                if expiry_dates:
                    self.expiry_dropdown.addItems(expiry_dates)

                    # Set default expiry if available
                    default_expiry = options_data.get('default_expiry')
                    if default_expiry and default_expiry in expiry_dates:
                        self.expiry_dropdown.setCurrentText(default_expiry)
                        self.density_chart.set_selected_expiry(default_expiry)
                    elif expiry_dates:
                        # Set first expiry as default
                        self.expiry_dropdown.setCurrentText(expiry_dates[0])
                        self.density_chart.set_selected_expiry(expiry_dates[0])

        except Exception as e:
            print(f"Error updating expiry dropdown: {e}")

    def update_charts_with_filtered_data(self, filtered_high_data, filtered_low_data, market_data=None):
        """Update all chart widgets with filtered data from backend.
        Ensures that in historical mode, the 'current price' passed to charts equals the historical close.
        """
        try:
            # Store current filtered data for options callbacks
            self._current_filtered_high_data = filtered_high_data
            self._current_filtered_low_data = filtered_low_data

            # Prepare market data for charts; adjust close[-1] to historical close in historical mode
            md_for_charts = market_data
            if market_data and getattr(self, 'viewing_historical', False) and getattr(self, 'historical_index', None) is not None:
                try:
                    close_arr = market_data.get('close')
                    if close_arr is not None and hasattr(close_arr, '__len__') and len(close_arr) > 0:
                        used_idx = min(self.historical_index, len(close_arr) - 1)
                        adjusted_close = close_arr.copy() if hasattr(close_arr, 'copy') else list(close_arr)
                        adjusted_close[-1] = float(close_arr[used_idx])
                        md_for_charts = dict(market_data)
                        md_for_charts['close'] = adjusted_close
                        print(f"Charts: using historical close at idx {used_idx} as current price: ${adjusted_close[-1]:.2f}")
                except Exception as e:
                    print(f"Charts: failed to adjust market_data.close for historical mode: {e}")

            # If in historical mode, rebase $ columns to the baseline close used for charts (without mutating table data)
            chart_high_data = filtered_high_data
            chart_low_data = filtered_low_data
            if getattr(self, 'viewing_historical', False) and isinstance(md_for_charts, dict):
                try:
                    close_series = md_for_charts.get('close')
                    if close_series is not None and hasattr(close_series, '__len__') and len(close_series) > 0:
                        baseline_close = float(close_series[-1])
                        chart_high_data = self._rebase_volatility_rows_to_close(filtered_high_data, baseline_close)
                        chart_low_data = self._rebase_volatility_rows_to_close(filtered_low_data, baseline_close)
                        print(f"Charts: rebased rows to historical baseline close ${baseline_close:.2f}")
                except Exception as e:
                    print(f"Charts: rebase to historical baseline failed: {e}")

            # Update volatility chart with market data for close price
            if hasattr(self, 'volatility_chart'):
                self.volatility_chart.update_data(chart_high_data, chart_low_data, md_for_charts)

            # Update density chart with market data
            if hasattr(self, 'density_chart'):
                self.density_chart.update_data(chart_high_data, chart_low_data, md_for_charts)

            # Update FWL odds chart with market data for last close price
            if hasattr(self, 'fwl_odds_chart'):
                self.fwl_odds_chart.update_data(chart_high_data, chart_low_data, md_for_charts)

        except Exception as e:
            print(f"Error updating charts with filtered data: {e}")

    def _increment_fwl_aggr(self):
        """Increment FWL Aggr. value with bounds checking."""
        try:
            current_value = int(self.fwl_aggr_input.text())
            if current_value < 10:
                self.fwl_aggr_input.setText(str(current_value + 1))
        except ValueError:
            self.fwl_aggr_input.setText("1")  # Reset to default if invalid

    def _decrement_fwl_aggr(self):
        """Decrement FWL Aggr. value with bounds checking."""
        try:
            current_value = int(self.fwl_aggr_input.text())
            if current_value > 1:
                self.fwl_aggr_input.setText(str(current_value - 1))
        except ValueError:
            self.fwl_aggr_input.setText("1")  # Reset to default if invalid

    def _validate_fwl_aggr_input(self):
        """Validate FWL Aggr. input and enforce bounds."""
        try:
            text = self.fwl_aggr_input.text()
            if text:  # Only validate if there's text
                value = int(text)
                if value < 1:
                    self.fwl_aggr_input.setText("1")
                elif value > 10:
                    self.fwl_aggr_input.setText("10")
        except ValueError:
            # Don't immediately reset on invalid input to allow typing
            pass

    def get_fwl_aggr_value(self):
        """Get the current FWL Aggr. value as an integer."""
        try:
            return int(self.fwl_aggr_input.text())
        except ValueError:
            return 1  # Default value

    def _on_fwl_aggr_changed(self):
        """Handle FWL Aggr value changes and trigger debounced data refresh."""
        try:
            # Only trigger refresh if the value is valid
            value = self.get_fwl_aggr_value()
            if 1 <= value <= 10:
                # Use debounce timer to prevent too many rapid refreshes
                self.fwl_aggr_timer.stop()  # Stop any existing timer
                self.fwl_aggr_timer.start(500)  # Wait 500ms before triggering refresh

        except Exception as e:
            print(f"Error handling FWL Aggr change: {e}")

    def _trigger_data_refresh(self):
        """Trigger data change on FWL updates without refetch; refetch only as fallback.
        - If FWL=5: switch locally to precomputed 5-FWL dataset (keeps current price stable).
        - Else: switch back to base dataset locally if available; otherwise fallback to centralized refresh.
        Historical mode is respected via apply_unified_filter(), which sets current price to the historical close.
        """
        try:
            value = self.get_fwl_aggr_value()
            print(f"[FWL AGGR] Handling change with FWL Aggr value: {value}")

            # Local switch paths (no refetch)
            if value == 5:
                print("[FWL AGGR] Using precomputed 5-FWL locally (no refetch)")
                self._use_precomputed_5fwl_local()
                return
            else:
                if hasattr(self, '_base_original_high_data') and getattr(self, '_base_original_high_data', None):
                    print("[FWL AGGR] Restoring base dataset locally (no refetch)")
                    self._use_base_original_local()
                    return

            # Fallback: centralized refetch
            if hasattr(self, '_main_window_ref') and self._main_window_ref:
                main_window = self._main_window_ref
                if hasattr(main_window, 'universal_controls'):
                    main_window.universal_controls.refresh_current_data()
                    print(f"[FWL AGGR] Fallback refetch triggered with FWL Aggr: {value}")
        except Exception as e:
            print(f"Error triggering data refresh: {e}")


    def _use_precomputed_5fwl_local(self):
        """Switch to precomputed 5-FWL datasets locally without refetching.
        Applies unified filter afterwards so historical mode uses the correct historical close.
        """
        try:
            if not hasattr(self, '_market_data') or not self._market_data:
                print("[FWL LOCAL] No market data available for local 5-FWL switch")
                return

            # Prefer cached copies captured on last update_data
            high_5 = getattr(self, '_precomputed_high_data_5fwl', None)
            low_5 = getattr(self, '_precomputed_low_data_5fwl', None)

            if not high_5 or not low_5:
                # Fallback to arrays on the market_data payload
                high_5 = self._market_data.get('volatility_high_data_5fwl', [])
                low_5 = self._market_data.get('volatility_low_data_5fwl', [])

            if not high_5 or not low_5:
                print("[FWL LOCAL] No 5-FWL datasets available; skipping local switch")
                return

            # Use copies to avoid accidental shared mutations
            self._original_high_data = list(high_5)
            self._original_low_data = list(low_5)
            self._last_fwl_local_mode = '5fwl'

            # If viewing historical, rebase projected values to historical close
            if getattr(self, 'viewing_historical', False):
                try:
                    hist_close = None
                    if hasattr(self, '_market_data') and self._market_data:
                        closes = self._market_data.get('close')
                        if closes is not None and hasattr(closes, '__len__') and len(closes) > 0:
                            hist_close = float(closes[-1])
                    orig_curr_close = None
                    if hasattr(self, 'original_close_price') and self.original_close_price is not None:
                        if hasattr(self.original_close_price, '__len__') and len(self.original_close_price) > 0:
                            orig_curr_close = float(self.original_close_price[-1])
                    if hist_close is not None and orig_curr_close is not None:
                        print(f"[FWL LOCAL] Rebasing 5-FWL projected values from ${orig_curr_close:.2f} to historical ${hist_close:.2f}")
                        self._recalculate_projected_values_for_historical_close(orig_curr_close, hist_close, self._market_data)
                except Exception as e:
                    print(f"[FWL LOCAL] Failed to rebase 5-FWL for historical close: {e}")

            # Re-apply filtering and historical close adjustments
            self.apply_unified_filter()
            print("[FWL LOCAL] Switched to precomputed 5-FWL locally and reapplied filters")
        except Exception as e:
            print(f"[FWL LOCAL] Error switching to precomputed 5-FWL: {e}")

    def _use_base_original_local(self):
        """Restore the base (non-5-FWL) datasets locally without refetching.
        Applies unified filter afterwards so historical mode uses the correct historical close.
        """
        try:
            base_high = getattr(self, '_base_original_high_data', None)
            base_low = getattr(self, '_base_original_low_data', None)

            if not base_high or not base_low:
                print("[FWL LOCAL] No base datasets cached; cannot restore locally")
                return

            self._original_high_data = list(base_high)
            self._original_low_data = list(base_low)
            self._last_fwl_local_mode = 'base'

            # If viewing historical, rebase projected values to historical close
            if getattr(self, 'viewing_historical', False):
                try:
                    hist_close = None
                    if hasattr(self, '_market_data') and self._market_data:
                        closes = self._market_data.get('close')
                        if closes is not None and hasattr(closes, '__len__') and len(closes) > 0:
                            hist_close = float(closes[-1])
                    orig_curr_close = None
                    if hasattr(self, 'original_close_price') and self.original_close_price is not None:
                        if hasattr(self.original_close_price, '__len__') and len(self.original_close_price) > 0:
                            orig_curr_close = float(self.original_close_price[-1])
                    if hist_close is not None and orig_curr_close is not None:
                        print(f"[FWL LOCAL] Rebasing BASE projected values from ${orig_curr_close:.2f} to historical ${hist_close:.2f}")
                        self._recalculate_projected_values_for_historical_close(orig_curr_close, hist_close, self._market_data)
                except Exception as e:
                    print(f"[FWL LOCAL] Failed to rebase BASE for historical close: {e}")

            self.apply_unified_filter()
            print("[FWL LOCAL] Restored base datasets locally and reapplied filters")
        except Exception as e:
            print(f"[FWL LOCAL] Error restoring base datasets: {e}")

    def set_main_window_ref(self, main_window):
        """Set reference to main window for triggering data refresh."""
        self._main_window_ref = main_window

    def get_occurrence_count(self):
        """Get the current occurrence count value.

        Returns:
            int: The current occurrence count value, or 0 if invalid (0 means all occurrences)
        """
        try:
            return int(self.occurrence_input.text())
        except (ValueError, AttributeError):
            return 0

    def _on_occurrence_changed(self):
        """Handle occurrence limiter value changes and trigger data refresh."""
        try:
            value = int(self.occurrence_input.text())
            if value >= 0:
                print(f"[OCCURRENCE LIMITER] Value changed to: {value}")
                # Apply the occurrence limit to current data
                self._apply_occurrence_limit()
        except ValueError:
            pass  # Invalid input, ignore

    def _validate_occurrence_input(self):
        """Validate occurrence input and apply styling."""
        try:
            value = int(self.occurrence_input.text())
            if value >= 0:
                # Valid input - normal styling
                self.occurrence_input.setStyleSheet("""
                    QLineEdit {
                        background-color: #2b2b2b;
                        color: #ffffff;
                        border: 1px solid #555555;
                        border-radius: 3px;
                        padding: 2px;
                    }
                """)
            else:
                # Invalid input - red border
                self.occurrence_input.setStyleSheet("""
                    QLineEdit {
                        background-color: #2b2b2b;
                        color: #ffffff;
                        border: 2px solid #ff4444;
                        border-radius: 3px;
                        padding: 2px;
                    }
                """)
        except ValueError:
            # Invalid input - red border
            self.occurrence_input.setStyleSheet("""
                QLineEdit {
                    background-color: #2b2b2b;
                    color: #ffffff;
                    border: 2px solid #ff4444;
                    border-radius: 3px;
                    padding: 2px;
                }
            """)

    def _increment_occurrence(self):
        """Increment occurrence value by 1."""
        try:
            current_value = int(self.occurrence_input.text())
            new_value = current_value + 1  # No max limit
            self.occurrence_input.setText(str(new_value))
        except ValueError:
            self.occurrence_input.setText("0")

    def _decrement_occurrence(self):
        """Decrement occurrence value by 1."""
        try:
            current_value = int(self.occurrence_input.text())
            new_value = max(current_value - 1, 0)  # Min value of 0
            self.occurrence_input.setText(str(new_value))
        except ValueError:
            self.occurrence_input.setText("0")

    def _apply_occurrence_limit(self):
        """Apply the occurrence limit to the current data."""
        try:
            occurrence_count = self.get_occurrence_count()

            if occurrence_count == 0:
                # Use all data - revert to original data
                if hasattr(self, '_original_high_data') and hasattr(self, '_original_low_data'):
                    # Apply current filter to original data
                    filter_result = self.data_service.apply_volatility_statistics_filter(
                        self._original_high_data, self._original_low_data, self._unified_filter_type
                    )
                    filtered_high = filter_result.get("volatility_filtered_high_data", [])
                    filtered_low = filter_result.get("volatility_filtered_low_data", [])

                    # Update tables
                    self._update_tables_with_data(filtered_high, filtered_low)
            else:
                # Limit to the latest N occurrences
                if hasattr(self, '_original_high_data') and hasattr(self, '_original_low_data'):
                    # Apply filter first
                    filter_result = self.data_service.apply_volatility_statistics_filter(
                        self._original_high_data, self._original_low_data, self._unified_filter_type
                    )
                    filtered_high = filter_result.get("volatility_filtered_high_data", [])
                    filtered_low = filter_result.get("volatility_filtered_low_data", [])

                    # Limit to latest N occurrences (chronologically)
                    # The data is already sorted chronologically, so take the last N items
                    if len(filtered_high) > occurrence_count:
                        filtered_high = filtered_high[-occurrence_count:]
                    if len(filtered_low) > occurrence_count:
                        filtered_low = filtered_low[-occurrence_count:]

                    # Update tables
                    self._update_tables_with_data(filtered_high, filtered_low)

        except Exception as e:
            print(f"Error applying occurrence limit: {e}")

    def _update_tables_with_data(self, filtered_high, filtered_low):
        """Update both tables with the provided data."""
        try:
            # Update high table
            if hasattr(self, 'high_table_model'):
                self.high_table_model.beginResetModel()
                self.high_table_model._data = filtered_high
                self.high_table_model.endResetModel()

            # Update low table
            if hasattr(self, 'low_table_model'):
                self.low_table_model.beginResetModel()
                self.low_table_model._data = filtered_low
                self.low_table_model.endResetModel()

            # Update charts if they exist
            if hasattr(self, '_market_data'):
                self.update_charts_with_filtered_data(filtered_high, filtered_low, self._market_data)

        except Exception as e:
            print(f"Error updating tables with data: {e}")

    def _on_zones_viewer_button_clicked(self):
        """Handle zones viewer button click - opens text viewer for zone prices."""
        try:
            # Check if user is on length 1 200DTL chart
            if not self._is_on_length_1_200dtl_chart():
                self._show_chart_requirement_popup()
                return

            # Create and show the zones text viewer dialog
            dialog = ZonesTextViewerDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"Error opening zones viewer: {e}")

    def _on_pivot_zone_viewer_button_clicked(self):
        """Handle pivot zone viewer button click - opens text viewer for pivot zone prices."""
        try:
            # Check if user is on length 1 200DTL chart
            if not self._is_on_length_1_200dtl_chart():
                self._show_chart_requirement_popup()
                return

            # Create and show the pivot zones text viewer dialog
            dialog = PivotZonesTextViewerDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"Error opening pivot zones viewer: {e}")

    def _is_on_length_1_200dtl_chart(self):
        """Check if the user is currently on the length 1 200DTL chart or viewing historical data."""
        try:
            # Allow zone calculations when viewing historical data
            if hasattr(self, 'viewing_historical') and self.viewing_historical:
                print("Historical data mode detected - bypassing chart requirement check")
                return True

            if hasattr(self, '_main_window_ref') and self._main_window_ref:
                main_window = self._main_window_ref
                if hasattr(main_window, 'universal_controls'):
                    current_values = main_window.universal_controls.get_current_values()
                    length = current_values.get('length', 0)
                    dtl = current_values.get('dtl', 0)

                    # Check if length is 1 and DTL is 200
                    return length == 1 and dtl == 200
            return False
        except Exception as e:
            print(f"Error checking chart configuration: {e}")
            return False

    def _show_chart_requirement_popup(self):
        """Show popup message requiring user to be on length 1 200DTL chart."""
        try:
            from PyQt6.QtWidgets import QMessageBox

            msg_box = QMessageBox()
            msg_box.setWindowTitle("Chart Configuration Required")
            msg_box.setText("Please configure your chart to Length 1 with 200 Days to Load (200DTL) to use this feature.")
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

            # Apply dark theme styling to match the application
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #1e1e1e;
                    color: #ffffff;
                }
                QMessageBox QPushButton {
                    background-color: #2b2b2b;
                    color: #ffffff;
                    border: 1px solid #555555;
                    padding: 6px 12px;
                    border-radius: 3px;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #3c3c3c;
                }
                QMessageBox QPushButton:pressed {
                    background-color: #1e1e1e;
                }
            """)

            msg_box.exec()
        except Exception as e:
            print(f"Error showing chart requirement popup: {e}")

    def _on_calculate_zones_button_clicked(self):
        """Handle calculate zones button click - starts zone calculation process."""
        try:
            # Check if user is on length 1 200DTL chart
            if not self._is_on_length_1_200dtl_chart():
                self._show_chart_requirement_popup()
                return

            # Start zone calculation process in backend
            self._start_zone_calculation()

        except Exception as e:
            print(f"Error starting zone calculation: {e}")

    def _on_pivot_zone_button_clicked(self):
        """Handle pivot zone button click - collect min avg for highs and min avg for lows from volatility graph."""
        try:
            # Check if user is on length 1 200DTL chart
            if not self._is_on_length_1_200dtl_chart():
                self._show_chart_requirement_popup()
                return

            print("Pivot Zone button clicked - collecting min avg for highs and min avg for lows from volatility graph")

            # Start pivot zone calculation process in backend
            self._start_pivot_zone_calculation()

        except Exception as e:
            print(f"Error with pivot zone button: {e}")
            import traceback
            traceback.print_exc()

    def _on_weekly_zones_button_clicked(self):
        """Handle weekly zones button click - collect min avg, median, average, max avg from current volatility."""
        try:
            # Check if user is on length 1 200DTL chart
            if not self._is_on_length_1_200dtl_chart():
                self._show_chart_requirement_popup()
                return

            print("Weekly Zones button clicked - collecting statistics from current volatility graph")

            # Start weekly zones calculation process
            self._start_weekly_zones_calculation()

        except Exception as e:
            print(f"Error with weekly zones button: {e}")
            import traceback
            traceback.print_exc()

    def _start_weekly_zones_calculation(self):
        """Start weekly zones calculation using precomputed 5-FWL dataset (no UI FWL change)."""
        try:
            # Get current market data
            if not hasattr(self, '_market_data') or not self._market_data:
                print("No market data available for weekly zones calculation")
                return

            print("Weekly Zones: using current volatility dataset")
            self._compute_weekly_zones()

        except Exception as e:
            print(f"Error in weekly zones calculation process: {e}")
            import traceback
            traceback.print_exc()

    def _compute_weekly_zones(self):
        """Compute weekly zones from current volatility data without changing controls."""
        try:
            # Always get market data reference first
            md = self._market_data

            # Use the 5-FWL projected OHLC table exactly (then extract volatility rows), so it matches Projected OHLC behavior
            projected_ohlc_5fwl = md.get('projected_ohlc_table_rows_5fwl', [])

            # Historical mode: slice projected OHLC rows to historical cutoff
            if getattr(self, 'viewing_historical', False):
                cutoff_idx = getattr(self, 'projected_ohlc_historical_cutoff', None)
                if cutoff_idx is None:
                    cutoff_idx = getattr(self, 'historical_index', None)
                if cutoff_idx is not None:
                    projected_ohlc_5fwl = projected_ohlc_5fwl[:cutoff_idx + 1]
                    print(f"Applied historical cutoff to 5-FWL projected OHLC: using {len(projected_ohlc_5fwl)} rows (<= index {cutoff_idx})")

            # Extract volatility rows from the 5-FWL projected OHLC using the same backend method
            volatility_high_data = []
            volatility_low_data = []
            try:
                if hasattr(self, 'data_service') and self.data_service and projected_ohlc_5fwl:
                    vs = self.data_service._prepare_volatility_statistics_data(projected_ohlc_5fwl)
                    volatility_high_data = vs.get('volatility_high_data', [])
                    volatility_low_data = vs.get('volatility_low_data', [])
            except Exception as e:
                print(f"Weekly Zones: failed to extract volatility rows from 5-FWL projected OHLC: {e}")

            # Fallback if extraction not available
            if not volatility_high_data or not volatility_low_data:
                volatility_high_data = md.get('volatility_high_data_5fwl', [])
                volatility_low_data = md.get('volatility_low_data_5fwl', [])
                # If historical, slice fallback arrays too
                if getattr(self, 'viewing_historical', False) and (getattr(self, 'historical_index', None) is not None or hasattr(self, 'projected_ohlc_historical_cutoff')):
                    cutoff_idx = getattr(self, 'projected_ohlc_historical_cutoff', None)
                    if cutoff_idx is None:
                        cutoff_idx = self.historical_index
                    volatility_high_data = volatility_high_data[:cutoff_idx + 1]
                    volatility_low_data = volatility_low_data[:cutoff_idx + 1]

            if not volatility_high_data or not volatility_low_data:
                print("No volatility data available for weekly zones calculation")
                return

            data_source = "5-FWL projected OHLC (historical)" if self.viewing_historical else "5-FWL projected OHLC (current)"
            print(f"Weekly zones using {len(volatility_high_data)} high and {len(volatility_low_data)} low entries from {data_source}")


            # Prepare market data for calculation (adjust close for historical mode so current price equals historical close)
            md_for_calc = md
            if self.viewing_historical and (getattr(self, 'historical_index', None) is not None or hasattr(self, 'projected_ohlc_historical_cutoff')):
                try:
                    close_arr = md.get('close') if md else None
                    if close_arr is not None and hasattr(close_arr, '__len__') and len(close_arr) > 0:
                        idx_pref = getattr(self, 'projected_ohlc_historical_cutoff', None)
                        used_idx = idx_pref if idx_pref is not None else self.historical_index
                        used_idx = min(used_idx, len(close_arr) - 1)
                        adjusted_close = close_arr.copy() if hasattr(close_arr, 'copy') else list(close_arr)
                        adjusted_close[-1] = float(close_arr[used_idx])
                        md_for_calc = dict(md)
                        md_for_calc['close'] = adjusted_close
                        print(f"Weekly Zones: using historical close at idx {used_idx} as current price: ${adjusted_close[-1]:.2f}")
                except Exception as e:
                    print(f"Weekly Zones: failed to adjust market_data.close for historical mode: {e}")


                # Rebase volatility rows to the historical baseline close so $ values reflect historical mode
                try:
                    close_series = md_for_calc.get('close') if isinstance(md_for_calc, dict) else None
                    if close_series is not None and hasattr(close_series, '__len__') and len(close_series) > 0:
                        baseline_close = float(close_series[-1])
                        volatility_high_data = self._rebase_volatility_rows_to_close(volatility_high_data, baseline_close)
                        volatility_low_data = self._rebase_volatility_rows_to_close(volatility_low_data, baseline_close)
                        print(f"Weekly Zones: rebased volatility rows to baseline close ${baseline_close:.2f}")
                except Exception as e:
                    print(f"Weekly Zones: rebase to historical baseline failed: {e}")

            if hasattr(self, 'data_service') and self.data_service:
                # 1. Calculate H/L matching statistics (filter_type = 0)
                hl_filter_result = self.data_service.apply_volatility_statistics_filter(
                    volatility_high_data, volatility_low_data, 0
                )
                hl_filtered_high = hl_filter_result.get('volatility_filtered_high_data', [])
                hl_filtered_low = hl_filter_result.get('volatility_filtered_low_data', [])
                hl_calc_data = self.data_service.calculate_volatility_data(
                    hl_filtered_high, hl_filtered_low, md_for_calc
                )

                # 2. Calculate weekday matching statistics (filter_type = 1)
                weekday_filter_result = self.data_service.apply_volatility_statistics_filter(
                    volatility_high_data, volatility_low_data, 1
                )
                weekday_filtered_high = weekday_filter_result.get('volatility_filtered_high_data', [])
                weekday_filtered_low = weekday_filter_result.get('volatility_filtered_low_data', [])
                weekday_calc_data = self.data_service.calculate_volatility_data(
                    weekday_filtered_high, weekday_filtered_low, md_for_calc
                )

                if hl_calc_data and weekday_calc_data:
                    # Extract H/L matching statistics
                    hl_highs_stats = hl_calc_data.get('highs_stats', {})
                    hl_lows_stats = hl_calc_data.get('lows_stats', {})

                    # Extract weekday matching statistics
                    weekday_highs_stats = weekday_calc_data.get('highs_stats', {})
                    weekday_lows_stats = weekday_calc_data.get('lows_stats', {})

                    # Cache for viewer
                    self._cached_weekly_zones_data = {
                        'hl_matching_highs_stats': hl_highs_stats,
                        'hl_matching_lows_stats': hl_lows_stats,
                        'weekday_matching_highs_stats': weekday_highs_stats,
                        'weekday_matching_lows_stats': weekday_lows_stats,
                        'fwl_setting': 5,
                        'calculation_metadata': {
                            'fwl_aggr_used': '5fwl',
                            # H/L matching values
                            'hl_highs_min_avg': hl_highs_stats.get('min_avg', 0),
                            'hl_highs_median': hl_highs_stats.get('median', 0),
                            'hl_highs_average': hl_highs_stats.get('average', 0),
                            'hl_highs_max_avg': hl_highs_stats.get('max_avg', 0),
                            'hl_lows_min_avg': hl_lows_stats.get('min_avg', 0),
                            'hl_lows_median': hl_lows_stats.get('median', 0),
                            'hl_lows_average': hl_lows_stats.get('average', 0),
                            'hl_lows_max_avg': hl_lows_stats.get('max_avg', 0),
                            # Weekday matching values
                            'weekday_highs_min_avg': weekday_highs_stats.get('min_avg', 0),
                            'weekday_highs_median': weekday_highs_stats.get('median', 0),
                            'weekday_highs_average': weekday_highs_stats.get('average', 0),
                            'weekday_highs_max_avg': weekday_highs_stats.get('max_avg', 0),
                            'weekday_lows_min_avg': weekday_lows_stats.get('min_avg', 0),
                            'weekday_lows_median': weekday_lows_stats.get('median', 0),
                            'weekday_lows_average': weekday_lows_stats.get('average', 0),
                            'weekday_lows_max_avg': weekday_lows_stats.get('max_avg', 0)
                        }
                    }
                    print("Weekly zones statistics collected and cached successfully")
                else:
                    print("Failed to calculate volatility data for weekly zones")
        except Exception as e:
            print(f"Error computing weekly zones: {e}")
            import traceback
            traceback.print_exc()

    def _complete_weekly_zones_calculation(self, original_fwl_aggr):
        """Complete the weekly zones calculation after data refresh."""
        try:
            print("Completing weekly zones calculation with current data...")

            # Get updated volatility data
            if not hasattr(self, '_market_data') or not self._market_data:
                print("No market data available after refresh")
                self.fwl_aggr_input.setText(str(original_fwl_aggr))
                return

            # Always use backend precomputed 5-FWL arrays for Weekly Zones (independent of UI FWL)
            volatility_high_data = self._market_data.get('volatility_high_data_5fwl', [])
            volatility_low_data = self._market_data.get('volatility_low_data_5fwl', [])

            # In historical mode, limit to cutoff index to match historical context
            if getattr(self, 'viewing_historical', False) and (getattr(self, 'historical_index', None) is not None or hasattr(self, 'projected_ohlc_historical_cutoff')):
                cutoff_idx = getattr(self, 'projected_ohlc_historical_cutoff', None)
                if cutoff_idx is None:
                    cutoff_idx = self.historical_index
                volatility_high_data = volatility_high_data[:cutoff_idx + 1]
                volatility_low_data = volatility_low_data[:cutoff_idx + 1]
                print(f"Applied historical cutoff to 5-FWL arrays: using {len(volatility_high_data)} high and {len(volatility_low_data)} low entries (<= index {cutoff_idx})")

            if not volatility_high_data or not volatility_low_data:
                print("No volatility data available for weekly zones calculation")
                self.fwl_aggr_input.setText(str(original_fwl_aggr))
                return

            print(f"Weekly zones calculation using {len(volatility_high_data)} high and {len(volatility_low_data)} low entries")

            # Calculate statistics for both H/L matching and weekday matching
            if hasattr(self, 'data_service') and self.data_service:
                # Prepare market data for calculation (adjust close for historical mode so current price equals historical close)
                base_md = self._market_data
                md_for_calc = base_md
                if self.viewing_historical and (getattr(self, 'historical_index', None) is not None or hasattr(self, 'projected_ohlc_historical_cutoff')):
                    try:
                        close_arr = base_md.get('close') if base_md else None
                        if close_arr is not None and hasattr(close_arr, '__len__') and len(close_arr) > 0:
                            idx_pref = getattr(self, 'projected_ohlc_historical_cutoff', None)
                            used_idx = idx_pref if idx_pref is not None else self.historical_index
                            used_idx = min(used_idx, len(close_arr) - 1)
                            adjusted_close = close_arr.copy() if hasattr(close_arr, 'copy') else list(close_arr)
                            adjusted_close[-1] = float(close_arr[used_idx])
                            md_for_calc = dict(base_md)
                            md_for_calc['close'] = adjusted_close
                            print(f"Weekly Zones: (post-refresh) using historical close at idx {used_idx} as current price: ${adjusted_close[-1]:.2f}")
                    except Exception as e:
                        print(f"Weekly Zones: (post-refresh) failed to adjust market_data.close for historical mode: {e}")

                # Rebase volatility rows to the historical baseline close so $ values reflect historical mode
                if self.viewing_historical:
                    try:
                        close_series = md_for_calc.get('close') if isinstance(md_for_calc, dict) else None
                        if close_series is not None and hasattr(close_series, '__len__') and len(close_series) > 0:
                            baseline_close = float(close_series[-1])
                            volatility_high_data = self._rebase_volatility_rows_to_close(volatility_high_data, baseline_close)
                            volatility_low_data = self._rebase_volatility_rows_to_close(volatility_low_data, baseline_close)
                            print(f"Weekly Zones (post-refresh): rebased rows to baseline close ${baseline_close:.2f}")
                    except Exception as e:
                        print(f"Weekly Zones (post-refresh): rebase to historical baseline failed: {e}")

                # 1. Calculate H/L matching statistics (filter_type = 0)
                hl_filter_result = self.data_service.apply_volatility_statistics_filter(
                    volatility_high_data, volatility_low_data, 0  # 0 = H/L matching
                )

                hl_filtered_high = hl_filter_result.get('volatility_filtered_high_data', [])
                hl_filtered_low = hl_filter_result.get('volatility_filtered_low_data', [])

                hl_calc_data = self.data_service.calculate_volatility_data(
                    hl_filtered_high, hl_filtered_low, md_for_calc
                )

                # 2. Calculate weekday matching statistics (filter_type = 1)
                weekday_filter_result = self.data_service.apply_volatility_statistics_filter(
                    volatility_high_data, volatility_low_data, 1  # 1 = weekday matching
                )

                weekday_filtered_high = weekday_filter_result.get('volatility_filtered_high_data', [])
                weekday_filtered_low = weekday_filter_result.get('volatility_filtered_low_data', [])

                weekday_calc_data = self.data_service.calculate_volatility_data(
                    weekday_filtered_high, weekday_filtered_low, md_for_calc
                )

                if hl_calc_data and weekday_calc_data:
                    # Extract H/L matching statistics
                    hl_highs_stats = hl_calc_data.get('highs_stats', {})
                    hl_lows_stats = hl_calc_data.get('lows_stats', {})

                    # Extract weekday matching statistics
                    weekday_highs_stats = weekday_calc_data.get('highs_stats', {})
                    weekday_lows_stats = weekday_calc_data.get('lows_stats', {})

                    print("=== WEEKLY ZONES STATISTICS ===")
                    print("H/L MATCHING:")
                    print(f"  HIGHS - Min Avg: ${hl_highs_stats.get('min_avg', 0):.2f}")
                    print(f"  HIGHS - Median: ${hl_highs_stats.get('median', 0):.2f}")
                    print(f"  HIGHS - Average: ${hl_highs_stats.get('average', 0):.2f}")
                    print(f"  HIGHS - Max Avg: ${hl_highs_stats.get('max_avg', 0):.2f}")
                    print(f"  LOWS - Min Avg: ${hl_lows_stats.get('min_avg', 0):.2f}")
                    print(f"  LOWS - Median: ${hl_lows_stats.get('median', 0):.2f}")
                    print(f"  LOWS - Average: ${hl_lows_stats.get('average', 0):.2f}")
                    print(f"  LOWS - Max Avg: ${hl_lows_stats.get('max_avg', 0):.2f}")

                    print("WEEKDAY MATCHING:")
                    print(f"  HIGHS - Min Avg: ${weekday_highs_stats.get('min_avg', 0):.2f}")
                    print(f"  HIGHS - Median: ${weekday_highs_stats.get('median', 0):.2f}")
                    print(f"  HIGHS - Average: ${weekday_highs_stats.get('average', 0):.2f}")
                    print(f"  HIGHS - Max Avg: ${weekday_highs_stats.get('max_avg', 0):.2f}")
                    print(f"  LOWS - Min Avg: ${weekday_lows_stats.get('min_avg', 0):.2f}")
                    print(f"  LOWS - Median: ${weekday_lows_stats.get('median', 0):.2f}")
                    print(f"  LOWS - Average: ${weekday_lows_stats.get('average', 0):.2f}")
                    print(f"  LOWS - Max Avg: ${weekday_lows_stats.get('max_avg', 0):.2f}")

                    # Store the weekly zones data for the viewer
                    self._cached_weekly_zones_data = {
                        'hl_matching_highs_stats': hl_highs_stats,
                        'hl_matching_lows_stats': hl_lows_stats,
                        'weekday_matching_highs_stats': weekday_highs_stats,
                        'weekday_matching_lows_stats': weekday_lows_stats,
                        'fwl_setting': 5,
                        'calculation_metadata': {
                            'fwl_aggr_used': '5fwl',
                            'original_fwl_aggr': original_fwl_aggr,
                            # H/L matching values
                            'hl_highs_min_avg': hl_highs_stats.get('min_avg', 0),
                            'hl_highs_median': hl_highs_stats.get('median', 0),
                            'hl_highs_average': hl_highs_stats.get('average', 0),
                            'hl_highs_max_avg': hl_highs_stats.get('max_avg', 0),
                            'hl_lows_min_avg': hl_lows_stats.get('min_avg', 0),
                            'hl_lows_median': hl_lows_stats.get('median', 0),
                            'hl_lows_average': hl_lows_stats.get('average', 0),
                            'hl_lows_max_avg': hl_lows_stats.get('max_avg', 0),
                            # Weekday matching values
                            'weekday_highs_min_avg': weekday_highs_stats.get('min_avg', 0),
                            'weekday_highs_median': weekday_highs_stats.get('median', 0),
                            'weekday_highs_average': weekday_highs_stats.get('average', 0),
                            'weekday_highs_max_avg': weekday_highs_stats.get('max_avg', 0),
                            'weekday_lows_min_avg': weekday_lows_stats.get('min_avg', 0),
                            'weekday_lows_median': weekday_lows_stats.get('median', 0),
                            'weekday_lows_average': weekday_lows_stats.get('average', 0),
                            'weekday_lows_max_avg': weekday_lows_stats.get('max_avg', 0)
                        }
                    }

                    print("Weekly zones statistics collected and cached successfully")
                else:
                    print("Failed to calculate volatility data for weekly zones")

            # Restore original FWL Aggr value
            self.fwl_aggr_input.setText(str(original_fwl_aggr))
            print(f"Restored FWL Aggr to original value: {original_fwl_aggr}")

            # Trigger another data refresh to restore original data
            if hasattr(self, '_main_window_ref') and self._main_window_ref:
                main_window = self._main_window_ref
                if hasattr(main_window, 'universal_controls'):
                    main_window.universal_controls.refresh_current_data()
                    print(f"Triggered final data refresh to restore original FWL Aggr: {original_fwl_aggr}")

        except Exception as e:
            print(f"Error completing weekly zones calculation: {e}")
            import traceback
            traceback.print_exc()

            # Ensure we restore the original FWL Aggr value
            try:
                self.fwl_aggr_input.setText(str(original_fwl_aggr))
                print(f"Restored FWL Aggr to original value after error: {original_fwl_aggr}")
            except:
                pass

    def _on_weekly_zones_viewer_button_clicked(self):
        """Handle weekly zones viewer button click - opens text viewer for weekly zone prices."""
        try:
            # Check if user is on length 1 200DTL chart
            if not self._is_on_length_1_200dtl_chart():
                self._show_chart_requirement_popup()
                return

            # Create and show the weekly zones text viewer dialog
            dialog = WeeklyZonesTextViewerDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"Error opening weekly zones viewer: {e}")

    def _on_historical_data_button_clicked(self):
        """Handle historical data button click - opens historical data selection dialog."""
        try:
            # Show historical data selection dialog
            self._show_historical_dialog()
        except Exception as e:
            print(f"Error opening historical data dialog: {e}")

    def _show_historical_dialog(self):
        """Show a dialog to select historical data by date and time"""
        try:
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QDateEdit, QTimeEdit, QPushButton
            from PyQt6.QtCore import QDate, QTime

            # Create a dialog
            dialog = QDialog(self)
            dialog.setWindowTitle("Select Historical Data")
            dialog.setMinimumWidth(400)
            dialog.setStyleSheet("background-color: #1e1e1e; color: #ffffff;")

            # Create a layout for the dialog
            dialog_layout = QVBoxLayout(dialog)
            dialog_layout.setContentsMargins(20, 20, 20, 20)
            dialog_layout.setSpacing(15)

            # Add a title
            title_label = QLabel("Select Historical Date and Time")
            title_label.setStyleSheet("""
                color: #ffffff;
                font-family: 'Segoe UI';
                font-weight: bold;
                font-size: 14px;
            """)
            dialog_layout.addWidget(title_label)

            # Add description
            desc_label = QLabel("Choose a date and time to analyze historical volatility data:")
            desc_label.setStyleSheet("color: #cccccc; font-family: 'Segoe UI'; font-size: 12px;")
            desc_label.setWordWrap(True)
            dialog_layout.addWidget(desc_label)

            # Create date picker
            date_layout = QHBoxLayout()
            date_label = QLabel("Date:")
            date_label.setStyleSheet("color: #ffffff; font-family: 'Segoe UI'; font-weight: bold;")
            date_picker = QDateEdit()
            date_picker.setDate(QDate.currentDate().addDays(-1))  # Default to yesterday
            date_picker.setStyleSheet("""
                QDateEdit {
                    background-color: #2b2b2b;
                    color: #ffffff;
                    border: 1px solid #555555;
                    border-radius: 3px;
                    padding: 5px;
                    font-family: 'Segoe UI';
                }
            """)
            date_layout.addWidget(date_label)
            date_layout.addWidget(date_picker)
            dialog_layout.addLayout(date_layout)

            # Create time picker
            time_layout = QHBoxLayout()
            time_label = QLabel("Time:")
            time_label.setStyleSheet("color: #ffffff; font-family: 'Segoe UI'; font-weight: bold;")
            time_picker = QTimeEdit()
            time_picker.setTime(QTime(18, 0))  # Default to 6:00 PM
            time_picker.setStyleSheet("""
                QTimeEdit {
                    background-color: #2b2b2b;
                    color: #ffffff;
                    border: 1px solid #555555;
                    border-radius: 3px;
                    padding: 5px;
                    font-family: 'Segoe UI';
                }
            """)
            time_layout.addWidget(time_label)
            time_layout.addWidget(time_picker)
            dialog_layout.addLayout(time_layout)

            # Create buttons
            button_layout = QHBoxLayout()
            button_layout.setContentsMargins(0, 10, 0, 0)
            button_layout.setSpacing(15)

            # Create the buttons
            load_button = QPushButton("Load Historical Data")
            load_button.setMinimumHeight(35)
            load_button.setStyleSheet("""
                QPushButton {
                    color: #ffffff;
                    background-color: #2b2b2b;
                    border: 1px solid #555555;
                    border-radius: 3px;
                    padding: 8px 15px;
                    font-family: 'Segoe UI';
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #3c3c3c;
                }
                QPushButton:pressed {
                    background-color: #1e1e1e;
                }
            """)

            cancel_button = QPushButton("Cancel")
            cancel_button.setMinimumHeight(35)
            cancel_button.setStyleSheet("""
                QPushButton {
                    color: #ffffff;
                    background-color: #2b2b2b;
                    border: 1px solid #555555;
                    border-radius: 3px;
                    padding: 8px 15px;
                    font-family: 'Segoe UI';
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #3c3c3c;
                }
                QPushButton:pressed {
                    background-color: #1e1e1e;
                }
            """)

            # Connect button signals
            load_button.clicked.connect(lambda: self._load_historical_from_dialog(dialog, date_picker, time_picker))
            cancel_button.clicked.connect(dialog.reject)

            # Add buttons to layout
            button_layout.addWidget(load_button)
            button_layout.addWidget(cancel_button)
            dialog_layout.addLayout(button_layout)

            # Show the dialog
            dialog.exec()

        except Exception as e:
            print(f"Error showing historical dialog: {e}")
            import traceback
            traceback.print_exc()

    def _load_historical_from_dialog(self, dialog, date_picker, time_picker):
        """Load historical data from the dialog"""
        try:
            import datetime

            # Get the selected date and time
            selected_date = date_picker.date().toPyDate()
            selected_time = time_picker.time().toPyTime()

            # Combine into a datetime object
            selected_datetime = datetime.datetime.combine(selected_date, selected_time)

            # Load the historical data
            self._load_historical_data(selected_datetime)

            # Close the dialog
            dialog.accept()

        except Exception as e:
            print(f"Error loading historical data: {e}")
            import traceback
            traceback.print_exc()

    def _load_historical_data(self, timestamp):
        """Load historical data for a specific timestamp and filter volatility data

        Args:
            timestamp (datetime): The timestamp to load data for
        """
        try:
            from PyQt6.QtWidgets import QMessageBox
            import datetime

            print(f"Loading historical data for: {timestamp}")

            # Check if we have market data and volatility data
            if not hasattr(self, '_market_data') or not self._market_data:
                QMessageBox.warning(self, "Error", "No market data available. Please load data first.")
                return

            if not hasattr(self, '_original_high_data') or not hasattr(self, '_original_low_data'):
                QMessageBox.warning(self, "Error", "No volatility data available. Please load data first.")
                return

            # Store original data if not already viewing historical
            if not self.viewing_historical:
                self.original_high_data = self._original_high_data.copy()
                self.original_low_data = self._original_low_data.copy()
                self.original_market_data = self._market_data.copy()
                print("Stored original volatility data for historical analysis")

            # Find the historical cutoff point in the market data
            market_data = self._market_data

            # Debug: Print available keys in market data
            print(f"Available market data keys: {list(market_data.keys()) if market_data else 'None'}")

            # Try different possible data sources for timestamps
            ohlc_data = None
            timestamp_data = None

            # Check for various possible data structures
            print(f"=== DEBUGGING TIMESTAMP MATCHING ===")
            print(f"Available market data keys: {list(market_data.keys())}")

            if 'ohlc_data' in market_data:
                ohlc_data = market_data['ohlc_data']
                print(f"Found ohlc_data with {len(ohlc_data)} entries")
            elif 'projected_ohlc_table_rows' in market_data:
                ohlc_data = market_data['projected_ohlc_table_rows']
                print(f"Found projected_ohlc_table_rows with {len(ohlc_data)} entries")
            elif 'timestamp' in market_data:
                timestamp_data = market_data['timestamp']
                print(f"Found timestamp data with {len(timestamp_data)} entries")

            if not ohlc_data and not timestamp_data:
                # Show available keys to help debug
                available_keys = list(market_data.keys()) if market_data else []
                print(f"ERROR: No OHLC or timestamp data found. Available keys: {available_keys}")
                QMessageBox.warning(self, "Error",
                    f"No OHLC or timestamp data available for historical filtering.\n\n"
                    f"Available data keys: {available_keys}")
                return

            # Find the LAST timestamp that is <= the selected timestamp (not closest!)
            print(f"Looking for last timestamp <= {timestamp}")
            cutoff_idx = None
            data_source = None
            matched_timestamp = None

            if ohlc_data:
                # Try OHLC data format - find LAST date <= selected date
                print(f"Scanning {len(ohlc_data)} OHLC entries for cutoff date...")
                for i, ohlc_row in enumerate(ohlc_data):
                    try:
                        # Handle different possible timestamp formats
                        if isinstance(ohlc_row, (list, tuple)) and len(ohlc_row) > 0:
                            ohlc_timestamp = ohlc_row[0]  # First element is timestamp
                        elif isinstance(ohlc_row, dict) and 'timestamp' in ohlc_row:
                            ohlc_timestamp = ohlc_row['timestamp']
                        else:
                            continue

                        # Convert string timestamps to datetime
                        if isinstance(ohlc_timestamp, str):
                            try:
                                ohlc_timestamp = datetime.datetime.fromisoformat(ohlc_timestamp.replace('Z', '+00:00'))
                            except:
                                continue

                        # Only consider dates that are <= selected timestamp
                        if ohlc_timestamp <= timestamp:
                            cutoff_idx = i  # Keep updating to get the LAST valid index
                            matched_timestamp = ohlc_timestamp
                            data_source = "ohlc_data"
                            print(f"Valid entry at index {i}: {ohlc_timestamp}")
                        else:
                            print(f"Entry {i} is after selected date: {ohlc_timestamp} > {timestamp}")

                    except Exception as e:
                        print(f"Error processing OHLC row {i}: {e}")
                        continue

            elif timestamp_data:
                # Try timestamp array format - find LAST date <= selected date
                print(f"Scanning {len(timestamp_data)} timestamp entries for cutoff date...")
                for i, ts in enumerate(timestamp_data):
                    try:
                        if isinstance(ts, str):
                            ts = datetime.datetime.fromisoformat(ts.replace('Z', '+00:00'))

                        # Only consider dates that are <= selected timestamp
                        if ts <= timestamp:
                            cutoff_idx = i  # Keep updating to get the LAST valid index
                            matched_timestamp = ts
                            data_source = "timestamp"
                            print(f"Valid entry at index {i}: {ts}")
                        else:
                            print(f"Entry {i} is after selected date: {ts} > {timestamp}")

                    except Exception as e:
                        print(f"Error processing timestamp {i}: {e}")
                        continue

            if cutoff_idx is None:
                QMessageBox.warning(self, "Error", f"Could not find any data on or before {timestamp}.")
                return

            print(f"SUCCESS: Found cutoff at index {cutoff_idx} using {data_source}")
            print(f"Matched timestamp: {matched_timestamp}")
            print(f"Will use ALL data from index 0 to {cutoff_idx} (inclusive)")

            # Set historical filtering parameters (don't cut off data, just set limits)
            print(f"Setting historical filter: will limit calculations to data up to index {cutoff_idx}")
            print(f"Original data preserved: {len(self.original_high_data)} high, {len(self.original_low_data)} low entries")

            # Set the viewing historical flag and filter parameters
            self.viewing_historical = True
            self.historical_timestamp = timestamp
            self.historical_index = cutoff_idx
            self.historical_cutoff_date = matched_timestamp

            # Enable the "Back to Current" button
            self.back_to_current_button.setEnabled(True)

            # Store the historical filter cutoff for backend calculations
            # The data itself remains unchanged, but calculations will be limited
            print(f"Historical filter active: calculations will use data from index 0 to {cutoff_idx} (inclusive)")
            print(f"Total available data: {len(self.original_high_data)} entries")
            print(f"Historical analysis will use: {cutoff_idx + 1} entries")

            # CRITICAL: Filter projected_ohlc_table_rows for historical bias calculations
            print("Filtering projected OHLC data for historical bias calculations...")
            self._filter_projected_ohlc_for_historical_bias(cutoff_idx, market_data)

            # Update the market data to use historical closing price
            print(f"Attempting to get historical close price for index {cutoff_idx}")
            historical_close_price = self._get_historical_close_price(cutoff_idx, data_source, ohlc_data, timestamp_data)

            if historical_close_price is not None:
                print(f"Got historical close price: ${historical_close_price:.2f}")

                # Store original close price if not already stored
                if not hasattr(self, 'original_close_price'):
                    self.original_close_price = market_data.get('close', []).copy()
                    print(f"Stored original close price array with {len(self.original_close_price)} entries")

                # CRITICAL: Update market data with historical close price
                # The backend services extract current_close using close_prices[-1]
                # So we need to replace the LAST element in the close array
                close_data = market_data.get('close')
                if hasattr(close_data, '__len__') and hasattr(close_data, '__getitem__') and len(close_data) > 0:
                    try:
                        # Handle both lists and numpy arrays
                        if hasattr(close_data, 'copy'):
                            historical_close_array = close_data.copy()
                        else:
                            historical_close_array = list(close_data)

                        original_current_close = float(historical_close_array[-1])
                        historical_close_array[-1] = historical_close_price

                        # Update BOTH market_data and self._market_data
                        market_data['close'] = historical_close_array
                        self._market_data['close'] = historical_close_array

                        print(f"PRICE CHANGE: Updated current price from ${original_current_close:.2f} to historical close: ${historical_close_price:.2f}")
                        print(f"Updated close array: last element is now ${historical_close_array[-1]:.2f}")
                        print(f"Close array type after update: {type(historical_close_array)}")

                        # CRITICAL: Recalculate projected highs and lows based on historical close
                        print("Recalculating projected highs and lows for historical close price...")
                        self._recalculate_projected_values_for_historical_close(
                            original_current_close, historical_close_price, market_data
                        )

                    except Exception as e:
                        print(f"ERROR updating close array: {e}")
                        print(f"Close data type: {type(close_data)}")
                        print(f"Trying to force update...")

                        # Force update by creating new array
                        try:
                            import numpy as np
                            if hasattr(close_data, 'dtype'):  # numpy array
                                historical_close_array = np.array(close_data)
                            else:
                                historical_close_array = list(close_data)

                            original_current_close = float(historical_close_array[-1])
                            historical_close_array[-1] = historical_close_price

                            market_data['close'] = historical_close_array
                            self._market_data['close'] = historical_close_array
                            print(f"FORCE UPDATE SUCCESS: Updated current price from ${original_current_close:.2f} to historical close: ${historical_close_price:.2f}")

                            # CRITICAL: Recalculate projected highs and lows based on historical close
                            print("Recalculating projected highs and lows for historical close price...")
                            self._recalculate_projected_values_for_historical_close(
                                original_current_close, historical_close_price, market_data
                            )

                        except Exception as e2:
                            print(f"FORCE UPDATE FAILED: {e2}")
                else:
                    print(f"ERROR: Close data is not a valid array. Type: {type(close_data)}, Has length: {hasattr(close_data, '__len__')}")
            else:
                print("WARNING: Could not get historical close price, current price will remain unchanged")

            # Apply current filter to the historical data and update charts/tables
            print("Applying unified filter to update charts with historical data...")

            # CRITICAL: Ensure historical close price is properly set before backend calculations
            self._ensure_historical_close_price_is_applied()

            self.apply_unified_filter()

            # Format the timestamp for display
            timestamp_str = timestamp.strftime("%Y-%m-%d %H:%M:%S")

            # Get actual timestamp that was matched
            actual_timestamp = "Unknown"
            total_entries = 0

            if data_source == "ohlc_data" and ohlc_data:
                if isinstance(ohlc_data[cutoff_idx], (list, tuple)):
                    actual_timestamp = ohlc_data[cutoff_idx][0]
                elif isinstance(ohlc_data[cutoff_idx], dict):
                    actual_timestamp = ohlc_data[cutoff_idx].get('timestamp', 'Unknown')
                total_entries = len(ohlc_data)
            elif data_source == "timestamp" and timestamp_data:
                actual_timestamp = timestamp_data[cutoff_idx]
                total_entries = len(timestamp_data)

            # Show a message to the user
            entries_used = cutoff_idx + 1
            info_message = f"Historical data loaded for {timestamp_str}\n\n"
            info_message += f"Closest match: {actual_timestamp}\n"
            info_message += f"Using {entries_used} entries out of {total_entries} total entries\n"
            info_message += f"Data filter set at index {cutoff_idx} (all data preserved).\n\n"
            info_message += f"Charts and tables now show historical volatility analysis.\n"
            info_message += f"Click 'Back to Current Data' to return to current analysis."

            QMessageBox.information(
                self,
                "Historical Data Loaded",
                info_message
            )

            print(f"Historical data filter applied - calculations will use {entries_used} entries out of {total_entries} total")

        except Exception as e:
            print(f"Error loading historical data: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Error", f"Error loading historical data: {str(e)}")

    def _get_historical_close_price(self, closest_idx, data_source, ohlc_data, timestamp_data):
        """Get the actual historical closing price for the selected day

        Args:
            closest_idx: Index of the closest timestamp match
            data_source: Source of the data ("ohlc_data", "timestamp", etc.)
            ohlc_data: OHLC data array
            timestamp_data: Timestamp data array

        Returns:
            float: Historical closing price from that specific day or None if not found
        """
        try:
            print(f"=== DEBUGGING HISTORICAL CLOSE PRICE ===")
            print(f"Getting historical close price for index {closest_idx}, data_source: {data_source}")

            # Debug: Check what market data we have
            if hasattr(self, '_market_data'):
                print(f"Market data keys: {list(self._market_data.keys())}")
                if 'close' in self._market_data:
                    close_array = self._market_data['close']
                    print(f"Close array type: {type(close_array)}")
                    print(f"Close array value: {close_array}")
                    print(f"Close array length: {len(close_array) if hasattr(close_array, '__len__') else 'No length'}")

                    # Check if it's a valid array-like object (list, numpy array, etc.)
                    if hasattr(close_array, '__len__') and hasattr(close_array, '__getitem__') and len(close_array) > 0:
                        print(f"Close array first 5 elements: {close_array[:5]}")
                        print(f"Close array last 5 elements: {close_array[-5:]}")
                        print(f"Requested index {closest_idx} vs array length {len(close_array)}")

                        # Try to get the historical close
                        if closest_idx < len(close_array):
                            historical_close = float(close_array[closest_idx])
                            print(f"SUCCESS: Found historical close at index {closest_idx}: ${historical_close:.2f}")
                            return historical_close
                        else:
                            print(f"ERROR: Index {closest_idx} is out of range for close array (length: {len(close_array)})")

                            # Try previous day as fallback
                            prev_day_idx = closest_idx - 1
                            if prev_day_idx >= 0 and prev_day_idx < len(close_array):
                                prev_close = float(close_array[prev_day_idx])
                                print(f"FALLBACK: Using previous day close at index {prev_day_idx}: ${prev_close:.2f}")
                                return prev_close
                            else:
                                print(f"ERROR: Previous day index {prev_day_idx} also out of range")
                    else:
                        print(f"ERROR: Close array is not a valid array-like object or is empty")
                        print(f"Trying to extract close data from OHLC data instead...")

                        # Try to extract close prices from the OHLC data
                        # Get the OHLC data from the current market_data
                        if hasattr(self, '_market_data') and data_source in self._market_data:
                            ohlc_rows = self._market_data[data_source]
                            print(f"Trying to extract close from {data_source} with {len(ohlc_rows)} rows")

                            if closest_idx < len(ohlc_rows):
                                ohlc_row = ohlc_rows[closest_idx]
                                print(f"OHLC row {closest_idx} length: {len(ohlc_row)}")
                                print(f"OHLC row {closest_idx} content: {ohlc_row}")

                                # For projected_ohlc_table_rows, the structure is typically:
                                # [Date, Weekday, Category, $Change High, $Change Low, %Change High, %Change Low,
                                #  $Projected High Change, $Projected Low Change, Projected High, Projected Low,
                                #  Open ratio, Close ratio, bg_color, fg_color]

                                if isinstance(ohlc_row, (list, tuple)) and len(ohlc_row) > 10:
                                    # Try to find a reasonable close price value
                                    # Look for numeric values that could be close prices
                                    for i, value in enumerate(ohlc_row):
                                        try:
                                            if isinstance(value, (int, float)) and 100 < value < 10000:  # Reasonable price range
                                                print(f"Found potential close price at position {i}: ${value:.2f}")
                                                return float(value)
                                            elif isinstance(value, str):
                                                # Try to parse string as float
                                                try:
                                                    parsed_value = float(value.replace('$', '').replace(',', ''))
                                                    if 100 < parsed_value < 10000:
                                                        print(f"Found potential close price (parsed) at position {i}: ${parsed_value:.2f}")
                                                        return parsed_value
                                                except:
                                                    continue
                                        except:
                                            continue

                                print(f"Could not find valid close price in OHLC row")
                            else:
                                print(f"Index {closest_idx} out of range for OHLC data (length: {len(ohlc_rows)})")
                        else:
                            print(f"No {data_source} found in market data")
                else:
                    print(f"ERROR: No 'close' key in market data")
            else:
                print(f"ERROR: No _market_data attribute")

            print(f"FAILED: Could not determine historical close price for index {closest_idx}")
            return None

        except Exception as e:
            print(f"ERROR getting historical close price: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _recalculate_projected_values_for_historical_close(self, original_current_close, historical_close_price, market_data):
        """Recalculate projected highs and lows based on historical close price instead of current close"""
        try:
            print(f"=== RECALCULATING PROJECTED VALUES ===")
            print(f"Original current close: ${original_current_close:.2f}")
            print(f"Historical close: ${historical_close_price:.2f}")

            # Calculate the adjustment ratio
            adjustment_ratio = historical_close_price / original_current_close
            print(f"Adjustment ratio: {adjustment_ratio:.6f}")

            # Update volatility high data projected values
            if hasattr(self, '_original_high_data') and self._original_high_data:
                updated_high_data = []
                for row in self._original_high_data:
                    if len(row) > 6:  # Projected High is at index 6
                        updated_row = list(row)
                        try:
                            original_projected_high = float(row[6])
                            # Recalculate projected high relative to historical close
                            # projected_high = historical_close + (original_projected_high - original_current_close)
                            adjusted_projected_high = historical_close_price + (original_projected_high - original_current_close)
                            updated_row[6] = adjusted_projected_high
                            print(f"High: ${original_projected_high:.2f} -> ${adjusted_projected_high:.2f}")
                        except (ValueError, TypeError):
                            pass
                        updated_high_data.append(updated_row)
                    else:
                        updated_high_data.append(row)

                # Update the original data with recalculated values
                self._original_high_data = updated_high_data
                print(f"Updated {len(updated_high_data)} high data rows")

            # Update volatility low data projected values
            if hasattr(self, '_original_low_data') and self._original_low_data:
                updated_low_data = []
                for row in self._original_low_data:
                    if len(row) > 6:  # Projected Low is at index 6
                        updated_row = list(row)
                        try:
                            original_projected_low = float(row[6])
                            # Recalculate projected low relative to historical close
                            # projected_low = historical_close + (original_projected_low - original_current_close)
                            adjusted_projected_low = historical_close_price + (original_projected_low - original_current_close)
                            updated_row[6] = adjusted_projected_low
                            print(f"Low: ${original_projected_low:.2f} -> ${adjusted_projected_low:.2f}")
                        except (ValueError, TypeError):
                            pass
                        updated_low_data.append(updated_row)
                    else:
                        updated_low_data.append(row)

                # Update the original data with recalculated values
                self._original_low_data = updated_low_data
                print(f"Updated {len(updated_low_data)} low data rows")
            print("=== PROJECTED VALUES RECALCULATION COMPLETE ===")
        except Exception as e:
            print(f"ERROR recalculating projected values: {e}")
            import traceback
            traceback.print_exc()




    def _rebase_volatility_rows_to_close(self, rows, baseline_close):
        """Recompute $ columns in volatility rows to be relative to baseline_close.
        Expected row format: [Index, Weekday, Category, $Change, %Change, $Projected Change, Projected]
        We keep %Change (idx 4) as-is and recompute:
          - $Projected Change (idx 5) = (%Change/100) * baseline_close
          - Projected (idx 6) = baseline_close + $Projected Change
          - $Change (idx 3) is set equal to $Projected Change to keep semantics aligned
        """
        try:
            if not rows:
                return rows
            rebased = []
            for r in rows:
                try:
                    if isinstance(r, (list, tuple)) and len(r) > 6:
                        rr = list(r)
                        pct = rr[4]
                        pct_val = float(pct) if pct not in (None, "") else 0.0
                        proj_change = (pct_val * float(baseline_close)) / 100.0
                        rr[5] = proj_change
                        rr[6] = float(baseline_close) + proj_change
                        rr[3] = proj_change
                        rebased.append(rr)
                    else:
                        rebased.append(r)
                except Exception:
                    rebased.append(r)
            return rebased
        except Exception:
            return rows

    def _filter_projected_ohlc_for_historical_bias(self, cutoff_idx, market_data):
        """Set up historical filter for projected OHLC data (preserves original data)"""
        try:
            print(f"=== SETTING UP HISTORICAL FILTER FOR PROJECTED OHLC ===")

            # Get the original projected OHLC data
            original_projected_ohlc = market_data.get('projected_ohlc_table_rows', [])
            if not original_projected_ohlc:
                print("No projected OHLC data to filter")
                return

            print(f"Original projected OHLC data: {len(original_projected_ohlc)} rows")
            print(f"Historical filter will limit bias calculations to entries 0 to {cutoff_idx} (inclusive)")

            # Store original data if not already stored (for restoration later)
            if not hasattr(self, 'original_projected_ohlc_data'):
                self.original_projected_ohlc_data = original_projected_ohlc.copy()
                print(f"Stored original projected OHLC data with {len(self.original_projected_ohlc_data)} rows")

            # Store the cutoff index for bias calculations to use
            self.projected_ohlc_historical_cutoff = cutoff_idx

            print(f"SUCCESS: Historical filter configured for projected OHLC data")
            print(f"Bias calculations will use entries 0 to {cutoff_idx} (original data preserved)")

        except Exception as e:
            print(f"ERROR setting up projected OHLC historical filter: {e}")
            import traceback
            traceback.print_exc()

    def _ensure_historical_close_price_is_applied(self):
        """Ensure the historical close price is properly applied to all market data references"""
        try:
            if not self.viewing_historical or self.historical_index is None:
                return

            print(f"=== ENSURING HISTORICAL CLOSE PRICE IS APPLIED ===")

            # Get the historical close price again to ensure consistency
            historical_close_price = None

            # Try to get from stored original close price array
            if hasattr(self, 'original_close_price') and self.original_close_price is not None:
                if hasattr(self.original_close_price, '__len__') and self.historical_index < len(self.original_close_price):
                    historical_close_price = float(self.original_close_price[self.historical_index])
                    print(f"Got historical close from original array[{self.historical_index}]: ${historical_close_price:.2f}")

            # If we couldn't get it from original array, try current market data
            if historical_close_price is None and hasattr(self, '_market_data'):
                close_array = self._market_data.get('close')
                if close_array is not None and hasattr(close_array, '__len__'):
                    if self.historical_index < len(close_array):
                        historical_close_price = float(close_array[self.historical_index])
                        print(f"Got historical close from current array[{self.historical_index}]: ${historical_close_price:.2f}")

            if historical_close_price is None:
                print("WARNING: Could not determine historical close price")
                return

            # Ensure the close array's last element is the historical close price
            close_array = self._market_data.get('close')
            if close_array is not None and hasattr(close_array, '__len__') and len(close_array) > 0:
                current_last_close = float(close_array[-1])
                if abs(current_last_close - historical_close_price) > 0.01:  # If they're different
                    print(f"FIXING: Close array last element is ${current_last_close:.2f}, should be ${historical_close_price:.2f}")

                    # Update the close array
                    if hasattr(close_array, 'copy'):
                        updated_close_array = close_array.copy()
                    else:
                        updated_close_array = list(close_array)

                    updated_close_array[-1] = historical_close_price
                    self._market_data['close'] = updated_close_array

                    print(f"FIXED: Updated close array last element to ${historical_close_price:.2f}")
                else:
                    print(f"VERIFIED: Close array last element is correct: ${current_last_close:.2f}")

            # Debug output for backend verification
            print(f"DEBUG: Market data close array last element: ${self._market_data['close'][-1]:.2f}")
            print(f"DEBUG: This should be the historical close price that backend will extract")

        except Exception as e:
            print(f"ERROR ensuring historical close price: {e}")
            import traceback
            traceback.print_exc()

    def _reset_to_current_data(self):
        """Reset to current data and restore original volatility data"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            # Check if we're viewing historical data
            if not self.viewing_historical:
                print("Not currently viewing historical data")
                return

            # Restore original data if it was stored
            if hasattr(self, 'original_high_data') and hasattr(self, 'original_low_data'):
                self._original_high_data = self.original_high_data.copy()
                self._original_low_data = self.original_low_data.copy()
                print("Restored original volatility data")

            if hasattr(self, 'original_market_data'):
                self._market_data = self.original_market_data.copy()
                print("Restored original market data")

            # Restore original close price if it was stored
            if hasattr(self, 'original_close_price') and self.original_close_price is not None:
                self._market_data['close'] = self.original_close_price.copy()
                print("Restored original close price")
                self.original_close_price = None

            # Restore original projected OHLC data if it was stored
            if hasattr(self, 'original_projected_ohlc_data') and self.original_projected_ohlc_data is not None:
                self._market_data['projected_ohlc_table_rows'] = self.original_projected_ohlc_data.copy()
                print(f"Restored original projected OHLC data with {len(self.original_projected_ohlc_data)} rows")
                self.original_projected_ohlc_data = None

            # Reset the viewing historical flag and clear stored data
            self.viewing_historical = False
            self.historical_timestamp = None
            self.historical_index = None
            self.historical_day_high = None
            self.historical_day_low = None

            # Clear stored original data
            self.original_high_data = None
            self.original_low_data = None
            if hasattr(self, 'original_market_data'):
                self.original_market_data = None

            # Disable the "Back to Current" button
            self.back_to_current_button.setEnabled(False)

            # Apply current filter to the restored data and update charts/tables
            self.apply_unified_filter()

            print("Successfully reset to current data")

            # Show confirmation message
            QMessageBox.information(
                self,
                "Current Data Restored",
                "Returned to current data analysis mode.\nCharts and tables now show current volatility data."
            )

        except Exception as e:
            print(f"Error resetting to current data: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Error", f"Error resetting to current data: {str(e)}")

    def _format_time_difference(self, seconds):
        """Format a time difference in seconds to a human-readable string

        Args:
            seconds (float): Time difference in seconds

        Returns:
            str: Formatted time difference
        """
        if seconds < 60:
            return f"{seconds:.1f} seconds"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f} minutes"
        elif seconds < 86400:
            hours = seconds / 3600
            return f"{hours:.1f} hours"
        else:
            days = seconds / 86400
            return f"{days:.1f} days"

    def _refresh_with_historical_data(self):
        """Refresh the volatility statistics with historical data filtering"""
        try:
            print(f"Refreshing charts with historical data - timestamp: {self.historical_timestamp}")

            # Apply the current filter to the historical data
            if hasattr(self, 'apply_unified_filter'):
                self.apply_unified_filter()
                print("Applied unified filter to historical data")

        except Exception as e:
            print(f"Error refreshing with historical data: {e}")

    def _refresh_with_current_data(self):
        """Refresh the volatility statistics with current (non-historical) data"""
        try:
            print("Refreshing charts with current data")

            # Apply the current filter to the restored current data
            if hasattr(self, 'apply_unified_filter'):
                self.apply_unified_filter()
                print("Applied unified filter to current data")

        except Exception as e:
            print(f"Error refreshing with current data: {e}")

    def is_viewing_historical_data(self):
        """Check if currently viewing historical data

        Returns:
            bool: True if viewing historical data, False otherwise
        """
        return self.viewing_historical

    def calculate_zones_for_historical_data(self):
        """Calculate intraday zones specifically for historical data - uses EXACT same process as historical data loading."""
        try:
            print("Calculating intraday zones for historical data...")

            # Ensure we have historical data
            if not self.viewing_historical:
                print("Not viewing historical data - use regular zone calculation instead")
                return None

            # Use the EXACT same process as _load_historical_data()
            print("Applying historical data process for zone calculations...")

            # CRITICAL: Ensure historical close price is applied (same as _load_historical_data)
            self._ensure_historical_close_price_is_applied()

            # Apply unified filter (same as _load_historical_data)
            self.apply_unified_filter()

            # Now use the regular zone calculation method since data is properly filtered
            print("Calling regular zone calculation with historical data context...")
            return self._start_zone_calculation()

        except Exception as e:
            print(f"Error in historical zone calculation: {e}")
            import traceback
            traceback.print_exc()
            return None

    def calculate_pivot_zones_for_historical_data(self):
        """Calculate pivot zones specifically for historical data - uses EXACT same process as historical data loading."""
        try:
            print("Calculating pivot zones for historical data...")

            # Ensure we have historical data
            if not self.viewing_historical:
                print("Not viewing historical data - use regular pivot zone calculation instead")
                return None

            # Use the EXACT same process as _load_historical_data()
            print("Applying historical data process for pivot zone calculations...")

            # CRITICAL: Ensure historical close price is applied (same as _load_historical_data)
            self._ensure_historical_close_price_is_applied()

            # Apply unified filter (same as _load_historical_data)
            self.apply_unified_filter()

            # Now use the regular pivot zone calculation method since data is properly filtered
            print("Calling regular pivot zone calculation with historical data context...")
            return self._start_pivot_zone_calculation()

        except Exception as e:
            print(f"Error in historical pivot zone calculation: {e}")
            import traceback
            traceback.print_exc()
            return None

    def calculate_weekly_zones_for_historical_data(self):
        """Calculate weekly zones specifically for historical data - uses EXACT same process as historical data loading."""
        try:
            print("Calculating weekly zones for historical data...")

            # Ensure we have historical data
            if not self.viewing_historical:
                print("Not viewing historical data - use regular weekly zone calculation instead")
                return None

            # Use the EXACT same process as _load_historical_data()
            print("Applying historical data process for weekly zone calculations...")

            # CRITICAL: Ensure historical close price is applied (same as _load_historical_data)
            self._ensure_historical_close_price_is_applied()

            # Apply unified filter (same as _load_historical_data)
            self.apply_unified_filter()

            # Now use the regular weekly zone calculation method since data is properly filtered
            print("Calling regular weekly zone calculation with historical data context...")
            return self._start_weekly_zones_calculation()

        except Exception as e:
            print(f"Error in historical weekly zone calculation: {e}")
            import traceback
            traceback.print_exc()
            return None



    def get_historical_day_range(self):
        """Get the high and low prices of the day AFTER the selected historical day

        Returns:
            tuple: (high, low) prices of the next day after the historical day, or (None, None) if not viewing historical data
        """
        if self.viewing_historical and self.historical_day_high is not None and self.historical_day_low is not None:
            return (self.historical_day_high, self.historical_day_low)
        return (None, None)

    def _start_zone_calculation(self):
        """Start the zone calculation process - ALL CALCULATIONS IN BACKEND."""
        try:
            # Get current market data
            if not hasattr(self, '_market_data') or not self._market_data:
                print("No market data available for zone calculation")
                return

            # CRITICAL FIX: Always use original unfiltered data for zone calculations
            # The backend service will apply both H/L matching and weekday matching filters independently
            if self.viewing_historical:
                # For historical data, use the original data but with historical filtering applied
                print("Using original volatility data for historical zone calculation")
                volatility_high_data = self._original_high_data if hasattr(self, '_original_high_data') else self._market_data.get('volatility_high_data', [])
                volatility_low_data = self._original_low_data if hasattr(self, '_original_low_data') else self._market_data.get('volatility_low_data', [])

                # Apply historical cutoff if available
                if hasattr(self, 'historical_index') and self.historical_index is not None:
                    cutoff_idx = self.historical_index
                    volatility_high_data = volatility_high_data[:cutoff_idx + 1]
                    volatility_low_data = volatility_low_data[:cutoff_idx + 1]
                    print(f"Applied historical cutoff: using {len(volatility_high_data)} high and {len(volatility_low_data)} low entries")
            else:
                print("Using original volatility data for zone calculation")
                volatility_high_data = self._market_data.get('volatility_high_data', [])
                volatility_low_data = self._market_data.get('volatility_low_data', [])

            if not volatility_high_data or not volatility_low_data:
                print("No volatility data available for zone calculation")
                return

            print(f"Zone calculation using {len(volatility_high_data)} high and {len(volatility_low_data)} low entries")

            # Import and create volgraph zones service
            from backend.volgraph_zones import VolgraphZonesService
            zones_service = VolgraphZonesService()

            print("Starting backend zone calculation process...")

            # ALL CALCULATIONS HAPPEN IN BACKEND - Frontend only calls this method
            # Pass the current filter type so backend uses the current displayed context
            zones_result = zones_service.calculate_zones(
                volatility_high_data,
                volatility_low_data,
                self._market_data,
                None,  # calculation_params
                self._unified_filter_type  # current filter type for context
            )

            # Store the results for the zones viewer
            self._cached_zones_data = zones_result

            print(f"Backend zone calculation completed: {len(zones_result.zones)} zones calculated")

        except Exception as e:
            print(f"Error in backend zone calculation process: {e}")
            import traceback
            traceback.print_exc()

    def _start_pivot_zone_calculation(self):
        """Start pivot zone calculation process in backend - collect min avg for highs and max avg for lows from volatility graph."""
        try:
            # Get current market data
            if not hasattr(self, '_market_data') or not self._market_data:
                print("No market data available for pivot zone calculation")
                return

            # CRITICAL FIX: Always use original unfiltered data for pivot zone calculations
            # The backend service will apply both H/L matching and weekday matching filters independently
            if self.viewing_historical:
                # For historical data, use the original data but with historical filtering applied
                print("Using original volatility data for historical pivot zone calculation")
                volatility_high_data = self._original_high_data if hasattr(self, '_original_high_data') else self._market_data.get('volatility_high_data', [])
                volatility_low_data = self._original_low_data if hasattr(self, '_original_low_data') else self._market_data.get('volatility_low_data', [])

                # Apply historical cutoff if available
                if hasattr(self, 'historical_index') and self.historical_index is not None:
                    cutoff_idx = self.historical_index
                    volatility_high_data = volatility_high_data[:cutoff_idx + 1]
                    volatility_low_data = volatility_low_data[:cutoff_idx + 1]
                    print(f"Applied historical cutoff: using {len(volatility_high_data)} high and {len(volatility_low_data)} low entries")
            else:
                print("Using original volatility data for pivot zone calculation")
                volatility_high_data = self._market_data.get('volatility_high_data', [])
                volatility_low_data = self._market_data.get('volatility_low_data', [])

            if not volatility_high_data or not volatility_low_data:
                print("No volatility data available for pivot zone calculation")
                return

            print(f"Pivot zone calculation using {len(volatility_high_data)} high and {len(volatility_low_data)} low entries")

            # Import and create pivot zone service
            from backend.pivot_zone import PivotZoneService
            pivot_service = PivotZoneService()

            print("Starting backend pivot zone calculation process...")

            # ALL CALCULATIONS HAPPEN IN BACKEND - Frontend only calls this method
            pivot_zones_result = pivot_service.calculate_pivot_zones_from_volatility_data(
                volatility_high_data,
                volatility_low_data,
                self._market_data
            )

            # Store the results for the pivot zones viewer
            self._cached_pivot_zones_data = pivot_zones_result

            print(f"Backend pivot zone calculation completed: {len(pivot_zones_result.zones)} pivot zones calculated")

            # Print summary of collected pivot values
            metadata = pivot_zones_result.calculation_metadata
            print(f"HL Matching - Highs Min Avg: {metadata.get('hl_matching_highs_min_avg', 0):.2f}")
            print(f"HL Matching - Lows Min Avg: {metadata.get('hl_matching_lows_min_avg', 0):.2f}")
            print(f"Weekday Matching - Highs Min Avg: {metadata.get('weekday_matching_highs_min_avg', 0):.2f}")
            print(f"Weekday Matching - Lows Min Avg: {metadata.get('weekday_matching_lows_min_avg', 0):.2f}")

            # Print optimal zones
            optimal_zones = metadata.get('optimal_zones', [])
            if optimal_zones:
                print("\nOptimal Zones:")

                # Separate and display min avg zones first
                min_avg_zones = [z for z in optimal_zones if z['type'] in ['highest_min_avg', 'lowest_min_avg']]
                range_zones = [z for z in optimal_zones if z['type'].startswith('range_')]

                for optimal_zone in min_avg_zones:
                    print(f"  {optimal_zone['type'].replace('_', ' ').title()}: ${optimal_zone['price']:.2f} ({optimal_zone['source_zone']})")

                # Display range levels if available
                if range_zones:
                    print("  Range Levels:")
                    range_zones.sort(key=lambda x: x['price'])
                    for range_zone in range_zones:
                        if range_zone['type'] == 'range_25_percent':
                            print(f"    25%: ${range_zone['price']:.2f}")
                        elif range_zone['type'] == 'range_50_percent':
                            print(f"    50% (Midpoint): ${range_zone['price']:.2f}")
                        elif range_zone['type'] == 'range_75_percent':
                            print(f"    75%: ${range_zone['price']:.2f}")

        except Exception as e:
            print(f"Error in backend pivot zone calculation process: {e}")
            import traceback
            traceback.print_exc()




class ZonesTextViewerDialog(QDialog):
    """Dialog for viewing zone prices in a text viewer."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Zone Prices Viewer")
        self.setModal(True)
        self.resize(600, 400)

        # Set dark theme styling
        self.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 10pt;
                padding: 8px;
            }
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 6px 12px;
                border-radius: 3px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
        """)

        self.setup_ui()
        self.load_zone_data()

    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Title label
        title_label = QLabel("Zone Prices")
        title_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #ffffff; margin-bottom: 5px;")
        layout.addWidget(title_label)

        # Text viewer
        self.text_viewer = QTextEdit()
        self.text_viewer.setReadOnly(True)
        layout.addWidget(self.text_viewer)

        # Button layout
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # Export to TradingView button
        export_button = QPushButton("Export to TradingView")
        export_button.clicked.connect(self._on_export_to_tv)
        button_layout.addWidget(export_button)

        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def _on_export_to_tv(self):
        """Generate and display TradingView PineScript for intraday zones."""
        try:
            # Get the parent widget to access cached zones data
            parent_widget = self.parent()
            if not hasattr(parent_widget, '_cached_zones_data') or not parent_widget._cached_zones_data:
                # Show error dialog
                from PyQt6.QtWidgets import QMessageBox
                msg_box = QMessageBox()
                msg_box.setIcon(QMessageBox.Icon.Warning)
                msg_box.setWindowTitle("No Zone Data")
                msg_box.setText("No zone data available. Please click 'Intraday Zones' first to calculate zones.")
                msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
                msg_box.exec()
                return

            # Generate TradingView PineScript
            pinescript_code = self._generate_tradingview_pinescript(parent_widget._cached_zones_data)

            # Create and show export dialog with the generated code
            export_dialog = ExportToTradingViewDialog(self)
            export_dialog.set_pinescript_content(pinescript_code)
            export_dialog.exec()

        except Exception as e:
            print(f"Error exporting to TradingView: {e}")
            import traceback
            traceback.print_exc()

    def _generate_tradingview_pinescript(self, zones_result):
        """Generate TradingView PineScript code for intraday zones."""
        try:
            import datetime

            # Get current date for header
            current_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Count zones and levels
            total_zones = len(zones_result.zones) if zones_result.zones else 0

            # Start building the PineScript
            lines = []

            # Header
            lines.append("//@version=6")
            lines.append("indicator('Intraday Zones Export', overlay = true, max_boxes_count = 500, max_lines_count = 500)")
            lines.append("")
            lines.append(f"// Generated on {current_date}")
            lines.append(f"// Exported {total_zones} intraday zones")
            lines.append("")

            # Variables to store boxes, lines and labels
            lines.append("// Variables to store boxes, lines and labels")
            lines.append("var box_array = array.new<box>()")
            lines.append("var line_array = array.new<line>()")
            lines.append("var label_array = array.new<label>()")
            lines.append("")

            # Clear previous items
            lines.append("// Clear previous items to prevent trail")
            lines.append("if barstate.islast")
            lines.append("    if array.size(box_array) > 0")
            lines.append("        for i = 0 to array.size(box_array) - 1 by 1")
            lines.append("            box.delete(array.get(box_array, i))")
            lines.append("        array.clear(box_array)")
            lines.append("    if array.size(line_array) > 0")
            lines.append("        for i = 0 to array.size(line_array) - 1 by 1")
            lines.append("            line.delete(array.get(line_array, i))")
            lines.append("        array.clear(line_array)")
            lines.append("    if array.size(label_array) > 0")
            lines.append("        for i = 0 to array.size(label_array) - 1 by 1")
            lines.append("            label.delete(array.get(label_array, i))")
            lines.append("        array.clear(label_array)")
            lines.append("")

            # Generate zones
            if zones_result.zones:
                for zone in zones_result.zones:
                    self._add_zone_to_pinescript(lines, zone)

            # Add summary comment
            lines.append(f"// Summary: Generated {total_zones} intraday zones")
            lines.append("// Items included in this script:")
            lines.append("// ZONES:")

            if zones_result.zones:
                for zone in zones_result.zones:
                    if hasattr(zone, 'upper_bound') and hasattr(zone, 'lower_bound'):
                        lines.append(f"// - {zone.level_name}: {zone.lower_bound:.4f} to {zone.upper_bound:.4f}")
                    else:
                        lines.append(f"// - {zone.level_name}: {zone.price:.4f}")

            return "\n".join(lines)

        except Exception as e:
            print(f"Error generating TradingView PineScript: {e}")
            import traceback
            traceback.print_exc()
            return "// Error generating PineScript code"

    def _add_zone_to_pinescript(self, lines, zone):
        """Add a single zone to the PineScript lines."""
        try:
            # Determine zone color based on type and level name
            zone_color, text_color = self._get_zone_colors(zone)

            # Clean up level name for display
            display_name = zone.level_name.replace('_', ' ')

            # Check if this is an extended zone with bounds
            if hasattr(zone, 'upper_bound') and hasattr(zone, 'lower_bound'):
                # Extended zone - create a box
                lines.append(f"// {display_name} from {zone.lower_bound:.4f} to {zone.upper_bound:.4f}")
                lines.append("if barstate.islast")
                lines.append(f"    zone_box = box.new(bar_index - 100, {zone.lower_bound:.4f}, bar_index + 100, {zone.upper_bound:.4f}, ")
                lines.append(f"                      border_color = {zone_color}, bgcolor = color.new({zone_color}, 85), ")
                lines.append("                      border_width = 1, extend = extend.both)")
                lines.append("    array.push(box_array, zone_box)")

                # Calculate center price for label
                center_price = (zone.upper_bound + zone.lower_bound) / 2
                lines.append(f"    zone_label = label.new(bar_index, {center_price:.4f}, ")
                lines.append(f"                          text = '{display_name}', style = label.style_none, ")
                lines.append(f"                          color = color.new(color.white, 100), textcolor = {text_color}, ")
                lines.append("                          size = size.small)")
                lines.append("    array.push(label_array, zone_label)")
            else:
                # Regular zone - create a line
                lines.append(f"// {display_name} at {zone.price:.4f}")
                lines.append("if barstate.islast")
                lines.append(f"    level_line = line.new(bar_index - 100, {zone.price:.4f}, bar_index + 100, {zone.price:.4f}, ")
                lines.append(f"                         color = {zone_color}, width = 2, extend = extend.both)")
                lines.append("    array.push(line_array, level_line)")
                lines.append(f"    level_label = label.new(bar_index + 50, {zone.price:.4f}, ")
                lines.append(f"                           text = '{display_name}: {zone.price:.4f}', style = label.style_none, ")
                lines.append(f"                           color = color.new(color.white, 100), textcolor = {text_color}, ")
                lines.append("                           size = size.small)")
                lines.append("    array.push(label_array, level_label)")

            lines.append("")

        except Exception as e:
            print(f"Error adding zone to PineScript: {e}")

    def _get_zone_colors(self, zone):
        """Get appropriate colors for a zone based on its properties."""
        try:
            # Default colors
            zone_color = "color.gray"
            text_color = "color.gray"

            # Color based on zone type and level name
            level_name = zone.level_name.lower()

            if 'maxavg' in level_name:
                zone_color = "color.rgb(0, 188, 212)"  # Cyan
                text_color = "color.rgb(0, 188, 212)"
            elif 'true_avg' in level_name:
                zone_color = "color.rgb(76, 175, 80)"  # Green
                text_color = "color.rgb(76, 175, 80)"
            elif 'avg_high_lowest_high' in level_name or 'avg_low_highest_low' in level_name:
                zone_color = "color.rgb(255, 235, 59)"  # Yellow
                text_color = "color.rgb(255, 235, 59)"
            elif 'minavg' in level_name:
                zone_color = "color.white"
                text_color = "color.white"
            else:
                zone_color = "color.gray"
                text_color = "color.gray"

            return zone_color, text_color

        except Exception as e:
            print(f"Error getting zone colors: {e}")
            return "color.gray", "color.gray"

    def load_zone_data(self):
        """Load and display zone price data."""
        try:
            # Get cached zones data from parent
            parent_widget = self.parent()
            if hasattr(parent_widget, '_cached_zones_data') and parent_widget._cached_zones_data:
                zones_result = parent_widget._cached_zones_data
                self._display_zones_data(zones_result)
            else:
                self.text_viewer.setPlainText("No zone data available. Please click 'Calculate Zones' first.")
        except Exception as e:
            self.text_viewer.setPlainText(f"Error loading zone data: {e}")

    def _display_zones_data(self, zones_result):
        """Display the calculated zones data in the text viewer."""
        try:
            output_lines = []
            output_lines.append("=== VOLGRAPH ZONES ===\n")

            # Display metadata
            metadata = zones_result.calculation_metadata
            calc_time = metadata.get('calculation_time_seconds', 0)
            output_lines.append(f"Calculation Time: {calc_time} seconds")
            output_lines.append(f"Total Zones: {metadata.get('total_zones', 0)}")
            output_lines.append(f"High Zones: {metadata.get('high_zones_count', 0)}")
            output_lines.append(f"Low Zones: {metadata.get('low_zones_count', 0)}")
            output_lines.append("")

            # Display H/L Matching Statistics
            hl_stats = metadata.get('hl_matching_stats', {})
            if hl_stats:
                output_lines.append("=== H/L MATCHING LEVELS ===")

                # H/L High Levels
                hl_highs = hl_stats.get('highs', {})
                if hl_highs:
                    output_lines.append("\nHIGH LEVELS (H/L Matching):")
                    output_lines.append(f"  MaxAvg: ${hl_highs.get('max_avg', 0):.2f}")
                    output_lines.append(f"  Average: ${hl_highs.get('average', 0):.2f}")
                    output_lines.append(f"  Median: ${hl_highs.get('median', 0):.2f}")
                    output_lines.append(f"  Count: {hl_highs.get('count', 0)}")

                # H/L Low Levels
                hl_lows = hl_stats.get('lows', {})
                if hl_lows:
                    output_lines.append("\nLOW LEVELS (H/L Matching):")
                    output_lines.append(f"  MaxAvg: ${hl_lows.get('max_avg', 0):.2f}")
                    output_lines.append(f"  Average: ${hl_lows.get('average', 0):.2f}")
                    output_lines.append(f"  Median: ${hl_lows.get('median', 0):.2f}")
                    output_lines.append(f"  Count: {hl_lows.get('count', 0)}")

                output_lines.append("")

            # Display Weekday Matching Statistics
            weekday_stats = metadata.get('weekday_matching_stats', {})
            if weekday_stats:
                output_lines.append("=== WEEKDAY MATCHING LEVELS ===")

                # Weekday High Levels
                weekday_highs = weekday_stats.get('highs', {})
                if weekday_highs:
                    output_lines.append("\nHIGH LEVELS (Weekday Matching):")
                    output_lines.append(f"  MaxAvg: ${weekday_highs.get('max_avg', 0):.2f}")
                    output_lines.append(f"  Average: ${weekday_highs.get('average', 0):.2f}")
                    output_lines.append(f"  Median: ${weekday_highs.get('median', 0):.2f}")
                    output_lines.append(f"  Count: {weekday_highs.get('count', 0)}")

                # Weekday Low Levels
                weekday_lows = weekday_stats.get('lows', {})
                if weekday_lows:
                    output_lines.append("\nLOW LEVELS (Weekday Matching):")
                    output_lines.append(f"  MaxAvg: ${weekday_lows.get('max_avg', 0):.2f}")
                    output_lines.append(f"  Average: ${weekday_lows.get('average', 0):.2f}")
                    output_lines.append(f"  Median: ${weekday_lows.get('median', 0):.2f}")
                    output_lines.append(f"  Count: {weekday_lows.get('count', 0)}")

                output_lines.append("")

            # Display extended zones (with ±0.033% ranges)
            extended_high_zones = metadata.get('extended_high_zones', {})
            extended_low_zones = metadata.get('extended_low_zones', {})
            extension_pct = metadata.get('extension_percentage', 0.033)

            if extended_high_zones or extended_low_zones:
                output_lines.append("=== OPTIMAL ZONES (FILTERED & EXTENDED) ===")
                output_lines.append(f"(Optimal levels filtered and extended by ±{extension_pct}%)")
                output_lines.append("")

                if extended_high_zones:
                    output_lines.append("OPTIMAL HIGH ZONES:")
                    for stat_type, data in extended_high_zones.items():
                        if isinstance(data, dict):
                            base_price = data.get('base_price', 0)
                            upper_bound = data.get('upper_bound', 0)
                            lower_bound = data.get('lower_bound', 0)
                            source = data.get('source', 'unknown')
                            count = data.get('count', 0)

                            output_lines.append(f"  {stat_type.upper()} ZONE (from {source}) - Count: {count}")
                            output_lines.append(f"    Base:  ${base_price:.2f}")
                            output_lines.append(f"    Upper: ${upper_bound:.2f} (+{extension_pct}%)")
                            output_lines.append(f"    Lower: ${lower_bound:.2f} (-{extension_pct}%)")
                            output_lines.append("")

                if extended_low_zones:
                    output_lines.append("OPTIMAL LOW ZONES:")
                    for stat_type, data in extended_low_zones.items():
                        if isinstance(data, dict):
                            base_price = data.get('base_price', 0)
                            upper_bound = data.get('upper_bound', 0)
                            lower_bound = data.get('lower_bound', 0)
                            source = data.get('source', 'unknown')
                            count = data.get('count', 0)

                            output_lines.append(f"  {stat_type.upper()} ZONE (from {source}) - Count: {count}")
                            output_lines.append(f"    Base:  ${base_price:.2f}")
                            output_lines.append(f"    Upper: ${upper_bound:.2f} (+{extension_pct}%)")
                            output_lines.append(f"    Lower: ${lower_bound:.2f} (-{extension_pct}%)")
                            output_lines.append("")

            # Display all calculated optimal zones (each base + extensions = 1 zone)
            if zones_result.zones:
                output_lines.append("=== FINAL OPTIMAL ZONES ===")
                for zone in zones_result.zones:
                    # Check if this is an extended zone
                    if hasattr(zone, 'upper_bound') and hasattr(zone, 'lower_bound'):
                        # Extended zone - show base + range
                        extension_pct = getattr(zone, 'extension_percentage', 0.033)
                        output_lines.append(f"ZONE: {zone.level_name} - ${zone.price:.2f} (±{extension_pct}%) [{zone.lower_bound:.2f} - {zone.upper_bound:.2f}] ({zone.zone_type.upper()}) - Count: {zone.count}")
                    else:
                        # Regular zone
                        output_lines.append(f"ZONE: {zone.level_name} - ${zone.price:.2f} ({zone.zone_type.upper()}) - Count: {zone.count}")
                output_lines.append("")

            # Join all lines and display
            display_text = "\n".join(output_lines)
            self.text_viewer.setPlainText(display_text)

        except Exception as e:
            self.text_viewer.setPlainText(f"Error displaying zones data: {e}")
            import traceback
            traceback.print_exc()

    def get_shared_options_data(self, market_data, selected_expiry=None):
        """Get options data shared across all charts in this tab instance."""
        try:
            import time

            ticker = market_data.get("ticker", "")
            if not ticker:
                return None

            # Determine expiry
            if selected_expiry is None:
                selected_expiry = getattr(self.density_chart, 'selected_expiry', None)
                if not selected_expiry:
                    # Get from any chart that has it
                    for chart in [self.density_chart, self.fwl_odds_chart]:
                        if hasattr(chart, 'selected_expiry') and chart.selected_expiry:
                            selected_expiry = chart.selected_expiry
                            break

                    if not selected_expiry:
                        # Fetch available dates and use first one
                        expiry_dates = self.density_chart._fetch_available_expiry_dates(ticker)
                        if expiry_dates:
                            selected_expiry = expiry_dates[0]
                        else:
                            return None

            # Check instance-level cache first (5 minute TTL)
            current_time = time.time()
            cache_key = (ticker, selected_expiry)

            if (self._shared_options_data and
                hasattr(self, '_shared_cache_key') and
                self._shared_cache_key == cache_key and
                current_time - self._shared_options_timestamp < 300):
                import logging
                logger = logging.getLogger(__name__)
                logger.debug(f"Using shared instance cache for {ticker} expiry {selected_expiry}")
                return self._shared_options_data

            # Fetch fresh data using the backend options service
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"Fetching shared options data for all charts: {ticker} expiry {selected_expiry}")
            options_data = self.data_service.get_options_data(ticker, selected_expiry) if self.data_service else None

            if options_data:
                # Cache at instance level
                self._shared_options_data = options_data
                self._shared_options_timestamp = current_time
                self._shared_cache_key = cache_key
                logger.debug(f"Cached shared options data for {ticker} expiry {selected_expiry}")

                # Also ensure all charts have the same selected expiry
                for chart in [self.density_chart, self.fwl_odds_chart]:
                    if hasattr(chart, 'selected_expiry'):
                        chart.selected_expiry = selected_expiry

            return options_data

        except Exception as e:
            print(f"Error getting shared options data: {e}")
            return None


class PivotZonesTextViewerDialog(QDialog):
    """Dialog for viewing pivot zone prices in a text viewer."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_tab = parent
        self.setWindowTitle("Pivot Zone Prices")
        self.setModal(True)
        self.resize(800, 600)

        # Apply dark theme
        self.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 10pt;
            }
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 6px 12px;
                border-radius: 3px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
        """)

        self.setup_ui()
        self.load_pivot_zone_data()

    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Title label
        title_label = QLabel("Pivot Zone Prices")
        title_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #ffffff; margin-bottom: 5px;")
        layout.addWidget(title_label)

        # Text viewer
        self.text_viewer = QTextEdit()
        self.text_viewer.setReadOnly(True)
        layout.addWidget(self.text_viewer)

        # Button layout
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # Export to TradingView button
        export_button = QPushButton("Export to TradingView")
        export_button.clicked.connect(self._on_export_to_tv)
        button_layout.addWidget(export_button)

        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def _on_export_to_tv(self):
        """Generate and display TradingView PineScript for pivot zones."""
        try:
            # Get the parent widget to access cached pivot zones data
            if not hasattr(self.parent_tab, '_cached_pivot_zones_data') or not self.parent_tab._cached_pivot_zones_data:
                # Show error dialog
                from PyQt6.QtWidgets import QMessageBox
                msg_box = QMessageBox()
                msg_box.setIcon(QMessageBox.Icon.Warning)
                msg_box.setWindowTitle("No Pivot Zone Data")
                msg_box.setText("No pivot zone data available. Please click 'Pivot Zone' first to calculate pivot zones.")
                msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
                msg_box.exec()
                return

            # Generate TradingView PineScript for pivot zones
            pinescript_code = self._generate_pivot_zones_pinescript(self.parent_tab._cached_pivot_zones_data)

            # Create and show export dialog with the generated code
            export_dialog = ExportToTradingViewDialog(self)
            export_dialog.set_pinescript_content(pinescript_code)
            export_dialog.exec()

        except Exception as e:
            print(f"Error exporting pivot zones to TradingView: {e}")
            import traceback
            traceback.print_exc()

    def _generate_pivot_zones_pinescript(self, pivot_zones_result):
        """Generate TradingView PineScript code for pivot zones with optimal high/low and 25/50/75% ranges."""
        try:
            import datetime

            # Get current date for header
            current_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Extract optimal zones from metadata
            metadata = pivot_zones_result.calculation_metadata if hasattr(pivot_zones_result, 'calculation_metadata') else {}
            optimal_zones = metadata.get('optimal_zones', [])

            # Find optimal high and low
            optimal_high = None
            optimal_low = None

            for zone in optimal_zones:
                if zone.get('type') == 'highest_min_avg':
                    optimal_high = zone.get('price')
                elif zone.get('type') == 'lowest_min_avg':
                    optimal_low = zone.get('price')

            if optimal_high is None or optimal_low is None:
                return "// Error: Could not find optimal high and low values for pivot zones"

            # Calculate 25% and 75% ranges between optimal high and low
            range_size = optimal_high - optimal_low
            range_25 = optimal_low + (range_size * 0.25)
            range_75 = optimal_low + (range_size * 0.75)

            # Start building the PineScript
            lines = []

            # Header
            lines.append("//@version=6")
            lines.append("indicator('Pivot Zones Export', overlay = true, max_lines_count = 500, max_boxes_count = 500)")
            lines.append("")
            lines.append(f"// Generated on {current_date}")
            lines.append(f"// Pivot zones with optimal high/low and 25/75% ranges")
            lines.append(f"// Optimal High: {optimal_high:.4f}")
            lines.append(f"// Optimal Low: {optimal_low:.4f}")
            lines.append("")

            # Variables to store lines and boxes
            lines.append("// Variables to store lines and boxes")
            lines.append("var line_array = array.new<line>()")
            lines.append("var box_array = array.new<box>()")
            lines.append("")

            # Clear previous items
            lines.append("// Clear previous items to prevent trail")
            lines.append("if barstate.islast")
            lines.append("    if array.size(line_array) > 0")
            lines.append("        for i = 0 to array.size(line_array) - 1 by 1")
            lines.append("            line.delete(array.get(line_array, i))")
            lines.append("        array.clear(line_array)")
            lines.append("    if array.size(box_array) > 0")
            lines.append("        for i = 0 to array.size(box_array) - 1 by 1")
            lines.append("            box.delete(array.get(box_array, i))")
            lines.append("        array.clear(box_array)")
            lines.append("")

            # Add colored fills between ranges
            self._add_pivot_range_fills_to_pinescript(lines, optimal_high, range_75, range_25, optimal_low)

            # Generate the 4 pivot lines (removed 50% range)
            self._add_pivot_line_to_pinescript(lines, optimal_high, "Optimal High", "color.white", 2)
            self._add_pivot_line_to_pinescript(lines, range_75, "75% Range", "color.blue", 1)
            self._add_pivot_line_to_pinescript(lines, range_25, "25% Range", "color.blue", 1)
            self._add_pivot_line_to_pinescript(lines, optimal_low, "Optimal Low", "color.white", 2)

            # Add summary comment
            lines.append("// Summary: Generated 4 pivot zone levels with colored range fills")
            lines.append("// Items included in this script:")
            lines.append(f"// - Blue fill: {range_75:.4f} to {optimal_high:.4f} (optimal high to 75%)")
            lines.append(f"// - Dark grey fill: {range_25:.4f} to {range_75:.4f} (25% to 75%)")
            lines.append(f"// - Blue fill: {optimal_low:.4f} to {range_25:.4f} (optimal low to 25%)")
            lines.append(f"// - Optimal High: {optimal_high:.4f} (white)")
            lines.append(f"// - 75% Range: {range_75:.4f} (blue)")
            lines.append(f"// - 25% Range: {range_25:.4f} (blue)")
            lines.append(f"// - Optimal Low: {optimal_low:.4f} (white)")

            return "\n".join(lines)

        except Exception as e:
            print(f"Error generating pivot zones PineScript: {e}")
            import traceback
            traceback.print_exc()
            return "// Error generating PineScript code"

    def _add_pivot_range_fills_to_pinescript(self, lines, optimal_high, range_75, range_25, optimal_low):
        """Add colored fills between the different ranges."""
        try:
            # Blue fill between optimal high and 75% range
            lines.append(f"// Blue fill from {range_75:.4f} to {optimal_high:.4f} (optimal high to 75%)")
            lines.append("if barstate.islast")
            lines.append(f"    blue_fill_top = box.new(bar_index - 100, {range_75:.4f}, bar_index + 100, {optimal_high:.4f}, ")
            lines.append("                           border_color = color.new(color.blue, 100), bgcolor = color.new(color.blue, 85), ")
            lines.append("                           border_width = 0, extend = extend.both)")
            lines.append("    array.push(box_array, blue_fill_top)")
            lines.append("")

            # Dark grey fill between 25% and 75% ranges
            lines.append(f"// Dark grey fill from {range_25:.4f} to {range_75:.4f} (25% to 75%)")
            lines.append("if barstate.islast")
            lines.append(f"    grey_fill_middle = box.new(bar_index - 100, {range_25:.4f}, bar_index + 100, {range_75:.4f}, ")
            lines.append("                              border_color = color.new(color.rgb(50, 50, 50), 100), bgcolor = color.new(color.rgb(50, 50, 50), 70), ")
            lines.append("                              border_width = 0, extend = extend.both)")
            lines.append("    array.push(box_array, grey_fill_middle)")
            lines.append("")

            # Blue fill between optimal low and 25% range
            lines.append(f"// Blue fill from {optimal_low:.4f} to {range_25:.4f} (optimal low to 25%)")
            lines.append("if barstate.islast")
            lines.append(f"    blue_fill_bottom = box.new(bar_index - 100, {optimal_low:.4f}, bar_index + 100, {range_25:.4f}, ")
            lines.append("                              border_color = color.new(color.blue, 100), bgcolor = color.new(color.blue, 85), ")
            lines.append("                              border_width = 0, extend = extend.both)")
            lines.append("    array.push(box_array, blue_fill_bottom)")
            lines.append("")

        except Exception as e:
            print(f"Error adding pivot range fills to PineScript: {e}")

    def _add_pivot_line_to_pinescript(self, lines, price, label_text, color, width):
        """Add a single pivot line to the PineScript lines."""
        try:
            lines.append(f"// {label_text} at {price:.4f}")
            lines.append("if barstate.islast")
            lines.append(f"    level_line = line.new(bar_index - 100, {price:.4f}, bar_index + 100, {price:.4f}, ")
            lines.append(f"                         color = {color}, width = {width}, extend = extend.both)")
            lines.append("    array.push(line_array, level_line)")
            lines.append("")

        except Exception as e:
            print(f"Error adding pivot line to PineScript: {e}")

    def load_pivot_zone_data(self):
        """Load and display pivot zone data."""
        try:
            # Get cached pivot zones data from parent
            parent_widget = self.parent_tab
            if hasattr(parent_widget, '_cached_pivot_zones_data') and parent_widget._cached_pivot_zones_data:
                pivot_zones_result = parent_widget._cached_pivot_zones_data
                self._display_pivot_zones_data(pivot_zones_result)
            else:
                # Show placeholder text if no data available
                output_lines = []
                output_lines.append("=== PIVOT ZONE PRICES ===")
                output_lines.append("")
                output_lines.append("No pivot zone data available. Please click 'Pivot Zone' button first.")
                output_lines.append("")
                output_lines.append("The Pivot Zone button collects min avg for highs and min avg for lows from:")
                output_lines.append("- 1fwl HL matching volatility chart")
                output_lines.append("- 1fwl Weekday matching volatility chart")
                output_lines.append("")

                self.text_viewer.setPlainText("\n".join(output_lines))
        except Exception as e:
            self.text_viewer.setPlainText(f"Error loading pivot zone data: {e}")

    def _display_pivot_zones_data(self, pivot_zones_result):
        """Display the calculated pivot zones data."""
        try:
            output_lines = []
            output_lines.append("=== PIVOT ZONE PRICES ===")
            output_lines.append("")

            # Display metadata with min avg values
            metadata = pivot_zones_result.calculation_metadata
            output_lines.append("=== COLLECTED PIVOT VALUES ===")
            output_lines.append(f"HL Matching - Highs Min Avg: ${metadata.get('hl_matching_highs_min_avg', 0):.2f}")
            output_lines.append(f"HL Matching - Lows Min Avg: ${metadata.get('hl_matching_lows_min_avg', 0):.2f}")
            output_lines.append(f"Weekday Matching - Highs Min Avg: ${metadata.get('weekday_matching_highs_min_avg', 0):.2f}")
            output_lines.append(f"Weekday Matching - Lows Min Avg: ${metadata.get('weekday_matching_lows_min_avg', 0):.2f}")
            output_lines.append("")

            # Display optimal zones section
            optimal_zones = metadata.get('optimal_zones', [])
            if optimal_zones:
                output_lines.append("=== OPTIMAL ZONES (HIGHEST & LOWEST MIN AVG) ===")

                # Separate optimal zones by type for better organization
                min_avg_zones = [z for z in optimal_zones if z['type'] in ['highest_min_avg', 'lowest_min_avg']]
                range_zones = [z for z in optimal_zones if z['type'].startswith('range_')]

                # Display the actual min avg zones first
                for optimal_zone in min_avg_zones:
                    zone_type_display = optimal_zone['type'].replace('_', ' ').title()
                    output_lines.append(f"{zone_type_display}: ${optimal_zone['price']:.2f}")
                    output_lines.append(f"  Source: {optimal_zone['source_zone']}")
                    output_lines.append(f"  Type: {optimal_zone['zone_type'].upper()}")
                    output_lines.append(f"  Count: {optimal_zone['count']}")
                    output_lines.append("")

                # Display range levels if we have both high and low
                if len(min_avg_zones) >= 2 and range_zones:
                    output_lines.append("--- RANGE LEVELS BETWEEN OPTIMAL ZONES ---")
                    # Sort range zones by percentage (25%, 50%, 75%)
                    range_zones.sort(key=lambda x: x['price'])
                    for range_zone in range_zones:
                        if range_zone['type'] == 'range_25_percent':
                            output_lines.append(f"25% Level: ${range_zone['price']:.2f}")
                        elif range_zone['type'] == 'range_50_percent':
                            output_lines.append(f"50% Level (Midpoint): ${range_zone['price']:.2f}")
                        elif range_zone['type'] == 'range_75_percent':
                            output_lines.append(f"75% Level: ${range_zone['price']:.2f}")
                    output_lines.append("")

            # Display calculation metadata
            output_lines.append("=== CALCULATION METADATA ===")
            output_lines.append(f"Total Zones: {metadata.get('total_zones', 0)}")
            output_lines.append(f"Pivot High Zones: {metadata.get('pivot_high_zones_count', 0)}")
            output_lines.append(f"Pivot Low Zones: {metadata.get('pivot_low_zones_count', 0)}")
            output_lines.append(f"Optimal Zones: {metadata.get('optimal_zones_count', 0)}")
            output_lines.append(f"Calculation Time: {metadata.get('calculation_time_seconds', 0):.4f} seconds")
            output_lines.append(f"Status: {metadata.get('status', 'unknown')}")
            output_lines.append("")



            self.text_viewer.setPlainText("\n".join(output_lines))

        except Exception as e:
            self.text_viewer.setPlainText(f"Error displaying pivot zones data: {e}")
            import traceback
            traceback.print_exc()

            # Set the text in the viewer
            self.text_viewer.setPlainText("\n".join(output_lines))

        except Exception as e:
            error_text = f"Error loading pivot zone data: {e}"
            self.text_viewer.setPlainText(error_text)
            print(error_text)


class WeeklyZonesTextViewerDialog(QDialog):
    """Dialog for viewing weekly zone prices in a text viewer."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_tab = parent
        self.setWindowTitle("Weekly Zone Prices")
        self.setModal(True)
        self.resize(800, 600)

        # Apply dark theme
        self.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 10pt;
                padding: 8px;
            }
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
            QLabel {
                color: #ffffff;
            }
        """)

        self.setup_ui()
        self.load_weekly_zones_data()

    def load_weekly_zones_data(self):
        """Load and display weekly zones data."""
        try:
            # Check if weekly zones data is available
            if hasattr(self.parent_tab, '_cached_weekly_zones_data') and self.parent_tab._cached_weekly_zones_data:
                self._display_weekly_zones_data(self.parent_tab._cached_weekly_zones_data)
            else:
                # Show placeholder text if no data available
                output_lines = []
                output_lines.append("=== WEEKLY ZONE PRICES ===")
                output_lines.append("")
                output_lines.append("No weekly zone data available. Please click 'Weekly Zones' button first.")
                output_lines.append("")
                output_lines.append("The Weekly Zones button will collect data from:")
                output_lines.append("- HL matching volatility chart")
                output_lines.append("- Weekday matching volatility chart")
                output_lines.append("")

                self.text_viewer.setPlainText("\n".join(output_lines))
        except Exception as e:
            self.text_viewer.setPlainText(f"Error loading weekly zone data: {e}")

    def _display_weekly_zones_data(self, weekly_zones_result):
        """Display the calculated weekly zones data."""
        try:
            output_lines = []
            output_lines.append("=== WEEKLY ZONE STATISTICS ===")
            output_lines.append("")

            # Display the collected statistics
            if weekly_zones_result and isinstance(weekly_zones_result, dict):
                hl_highs_stats = weekly_zones_result.get('hl_matching_highs_stats', {})
                hl_lows_stats = weekly_zones_result.get('hl_matching_lows_stats', {})
                weekday_highs_stats = weekly_zones_result.get('weekday_matching_highs_stats', {})
                weekday_lows_stats = weekly_zones_result.get('weekday_matching_lows_stats', {})

                if hl_highs_stats or hl_lows_stats or weekday_highs_stats or weekday_lows_stats:
                    output_lines.append("=== COLLECTED STATISTICS FROM CURRENT VOLATILITY ===")
                    output_lines.append("")

                    # Display H/L matching statistics
                    if hl_highs_stats or hl_lows_stats:
                        output_lines.append("=== H/L MATCHING ===")

                        if hl_highs_stats:
                            output_lines.append("HIGH LEVELS:")
                            output_lines.append(f"  Min Avg: ${hl_highs_stats.get('min_avg', 0):.2f}")
                            output_lines.append(f"  Median: ${hl_highs_stats.get('median', 0):.2f}")
                            output_lines.append(f"  Average: ${hl_highs_stats.get('average', 0):.2f}")
                            output_lines.append(f"  Max Avg: ${hl_highs_stats.get('max_avg', 0):.2f}")
                            output_lines.append("")

                        if hl_lows_stats:
                            output_lines.append("LOW LEVELS:")
                            output_lines.append(f"  Min Avg: ${hl_lows_stats.get('min_avg', 0):.2f}")
                            output_lines.append(f"  Median: ${hl_lows_stats.get('median', 0):.2f}")
                            output_lines.append(f"  Average: ${hl_lows_stats.get('average', 0):.2f}")
                            output_lines.append(f"  Max Avg: ${hl_lows_stats.get('max_avg', 0):.2f}")
                            output_lines.append("")

                    # Display weekday matching statistics
                    if weekday_highs_stats or weekday_lows_stats:
                        output_lines.append("=== WEEKDAY MATCHING ===")

                        if weekday_highs_stats:
                            output_lines.append("HIGH LEVELS:")
                            output_lines.append(f"  Min Avg: ${weekday_highs_stats.get('min_avg', 0):.2f}")
                            output_lines.append(f"  Median: ${weekday_highs_stats.get('median', 0):.2f}")
                            output_lines.append(f"  Average: ${weekday_highs_stats.get('average', 0):.2f}")
                            output_lines.append(f"  Max Avg: ${weekday_highs_stats.get('max_avg', 0):.2f}")
                            output_lines.append("")

                        if weekday_lows_stats:
                            output_lines.append("LOW LEVELS:")
                            output_lines.append(f"  Min Avg: ${weekday_lows_stats.get('min_avg', 0):.2f}")
                            output_lines.append(f"  Median: ${weekday_lows_stats.get('median', 0):.2f}")
                            output_lines.append(f"  Average: ${weekday_lows_stats.get('average', 0):.2f}")
                            output_lines.append(f"  Max Avg: ${weekday_lows_stats.get('max_avg', 0):.2f}")
                            output_lines.append("")

                    # Display optimal zones section
                    if (hl_highs_stats and weekday_highs_stats) or (hl_lows_stats and weekday_lows_stats):
                        output_lines.append("=== OPTIMAL ZONES (H/L ↔ WEEKDAY CONNECTIONS) ===")
                        output_lines.append("")

                        # High zones (connecting H/L to Weekday values)
                        if hl_highs_stats and weekday_highs_stats:
                            output_lines.append("HIGH ZONES:")

                            # Min Avg Zone: H/L min avg ↔ Weekday min avg
                            hl_min_avg = hl_highs_stats.get('min_avg', 0)
                            weekday_min_avg = weekday_highs_stats.get('min_avg', 0)
                            zone_min = min(hl_min_avg, weekday_min_avg)
                            zone_max = max(hl_min_avg, weekday_min_avg)
                            output_lines.append(f"  Min Avg Zone: ${zone_min:.2f} ↔ ${zone_max:.2f}")

                            # Median Zone: H/L median ↔ Weekday median
                            hl_median = hl_highs_stats.get('median', 0)
                            weekday_median = weekday_highs_stats.get('median', 0)
                            zone_min = min(hl_median, weekday_median)
                            zone_max = max(hl_median, weekday_median)
                            output_lines.append(f"  Median Zone: ${zone_min:.2f} ↔ ${zone_max:.2f}")

                            # Average Zone: H/L average ↔ Weekday average
                            hl_average = hl_highs_stats.get('average', 0)
                            weekday_average = weekday_highs_stats.get('average', 0)
                            zone_min = min(hl_average, weekday_average)
                            zone_max = max(hl_average, weekday_average)
                            output_lines.append(f"  Average Zone: ${zone_min:.2f} ↔ ${zone_max:.2f}")

                            # Max Avg Zone: H/L max avg ↔ Weekday max avg
                            hl_max_avg = hl_highs_stats.get('max_avg', 0)
                            weekday_max_avg = weekday_highs_stats.get('max_avg', 0)
                            zone_min = min(hl_max_avg, weekday_max_avg)
                            zone_max = max(hl_max_avg, weekday_max_avg)
                            output_lines.append(f"  Max Avg Zone: ${zone_min:.2f} ↔ ${zone_max:.2f}")
                            output_lines.append("")

                        # Low zones (connecting H/L to Weekday values)
                        if hl_lows_stats and weekday_lows_stats:
                            output_lines.append("LOW ZONES:")

                            # Min Avg Zone: H/L min avg ↔ Weekday min avg
                            hl_min_avg = hl_lows_stats.get('min_avg', 0)
                            weekday_min_avg = weekday_lows_stats.get('min_avg', 0)
                            zone_min = min(hl_min_avg, weekday_min_avg)
                            zone_max = max(hl_min_avg, weekday_min_avg)
                            output_lines.append(f"  Min Avg Zone: ${zone_min:.2f} ↔ ${zone_max:.2f}")

                            # Median Zone: H/L median ↔ Weekday median
                            hl_median = hl_lows_stats.get('median', 0)
                            weekday_median = weekday_lows_stats.get('median', 0)
                            zone_min = min(hl_median, weekday_median)
                            zone_max = max(hl_median, weekday_median)
                            output_lines.append(f"  Median Zone: ${zone_min:.2f} ↔ ${zone_max:.2f}")

                            # Average Zone: H/L average ↔ Weekday average
                            hl_average = hl_lows_stats.get('average', 0)
                            weekday_average = weekday_lows_stats.get('average', 0)
                            zone_min = min(hl_average, weekday_average)
                            zone_max = max(hl_average, weekday_average)
                            output_lines.append(f"  Average Zone: ${zone_min:.2f} ↔ ${zone_max:.2f}")

                            # Max Avg Zone: H/L max avg ↔ Weekday max avg
                            hl_max_avg = hl_lows_stats.get('max_avg', 0)
                            weekday_max_avg = weekday_lows_stats.get('max_avg', 0)
                            zone_min = min(hl_max_avg, weekday_max_avg)
                            zone_max = max(hl_max_avg, weekday_max_avg)
                            output_lines.append(f"  Max Avg Zone: ${zone_min:.2f} ↔ ${zone_max:.2f}")
                            output_lines.append("")

                else:
                    output_lines.append("No statistical data available.")
                    output_lines.append("")

            else:
                output_lines.append("No weekly zone data available.")
                output_lines.append("")

            self.text_viewer.setPlainText("\n".join(output_lines))

        except Exception as e:
            self.text_viewer.setPlainText(f"Error displaying weekly zone data: {e}")

    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Title label
        title_label = QLabel("Weekly Zone Prices")
        title_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #ffffff; margin-bottom: 5px;")
        layout.addWidget(title_label)

        # Text viewer
        self.text_viewer = QTextEdit()
        self.text_viewer.setReadOnly(True)
        layout.addWidget(self.text_viewer)

        # Button layout
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # Export to TradingView button
        export_button = QPushButton("Export to TradingView")
        export_button.clicked.connect(self._on_export_to_tv)
        button_layout.addWidget(export_button)

        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def _on_export_to_tv(self):
        """Generate and display TradingView PineScript for weekly zones."""
        try:
            # Get the parent widget to access cached weekly zones data
            if not hasattr(self.parent_tab, '_cached_weekly_zones_data') or not self.parent_tab._cached_weekly_zones_data:
                # Show error dialog
                from PyQt6.QtWidgets import QMessageBox
                msg_box = QMessageBox()
                msg_box.setIcon(QMessageBox.Icon.Warning)
                msg_box.setWindowTitle("No Weekly Zone Data")
                msg_box.setText("No weekly zone data available. Please click 'Weekly Zones' first to calculate weekly zones.")
                msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
                msg_box.exec()
                return

            # Generate TradingView PineScript for weekly zones
            pinescript_code = self._generate_weekly_zones_pinescript(self.parent_tab._cached_weekly_zones_data)

            # Create and show export dialog with the generated code
            export_dialog = ExportToTradingViewDialog(self)
            export_dialog.set_pinescript_content(pinescript_code)
            export_dialog.exec()

        except Exception as e:
            print(f"Error exporting weekly zones to TradingView: {e}")
            import traceback
            traceback.print_exc()

    def _generate_weekly_zones_pinescript(self, weekly_zones_data):
        """Generate TradingView PineScript code for weekly zones."""
        try:
            import datetime

            # Get current date for header
            current_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Convert weekly zones data to zone objects
            zone_objects = self._convert_weekly_zones_to_objects(weekly_zones_data)
            total_zones = len(zone_objects)

            # Start building the PineScript
            lines = []

            # Header
            lines.append("//@version=6")
            lines.append("indicator('Weekly Zones Export', overlay = true, max_boxes_count = 500, max_lines_count = 500)")
            lines.append("")
            lines.append(f"// Generated on {current_date}")
            lines.append(f"// Exported {total_zones} weekly zones")
            lines.append("")

            # Variables to store boxes, lines and labels
            lines.append("// Variables to store boxes, lines and labels")
            lines.append("var box_array = array.new<box>()")
            lines.append("var line_array = array.new<line>()")
            lines.append("var label_array = array.new<label>()")
            lines.append("")

            # Clear previous items
            lines.append("// Clear previous items to prevent trail")
            lines.append("if barstate.islast")
            lines.append("    if array.size(box_array) > 0")
            lines.append("        for i = 0 to array.size(box_array) - 1 by 1")
            lines.append("            box.delete(array.get(box_array, i))")
            lines.append("        array.clear(box_array)")
            lines.append("    if array.size(line_array) > 0")
            lines.append("        for i = 0 to array.size(line_array) - 1 by 1")
            lines.append("            line.delete(array.get(line_array, i))")
            lines.append("        array.clear(line_array)")
            lines.append("    if array.size(label_array) > 0")
            lines.append("        for i = 0 to array.size(label_array) - 1 by 1")
            lines.append("            label.delete(array.get(label_array, i))")
            lines.append("        array.clear(label_array)")
            lines.append("")

            # Generate weekly zones
            if zone_objects:
                for zone in zone_objects:
                    self._add_weekly_zone_to_pinescript(lines, zone)

            # Add summary comment
            lines.append(f"// Summary: Generated {total_zones} weekly zones")
            lines.append("// Items included in this script:")
            lines.append("// WEEKLY ZONES:")

            if zone_objects:
                for zone in zone_objects:
                    lines.append(f"// - {zone.level_name}: {zone.price:.4f}")

            return "\n".join(lines)

        except Exception as e:
            print(f"Error generating weekly zones PineScript: {e}")
            import traceback
            traceback.print_exc()
            return "// Error generating PineScript code"

    def _convert_weekly_zones_to_objects(self, weekly_zones_data):
        """Convert weekly zones data dictionary to zone objects for PineScript generation."""
        try:
            from dataclasses import dataclass

            @dataclass
            class WeeklyZone:
                price: float
                level_name: str
                zone_type: str = 'weekly'

            zone_objects = []

            # Extract H/L matching statistics
            hl_highs_stats = weekly_zones_data.get('hl_matching_highs_stats', {})
            hl_lows_stats = weekly_zones_data.get('hl_matching_lows_stats', {})

            # Extract weekday matching statistics
            weekday_highs_stats = weekly_zones_data.get('weekday_matching_highs_stats', {})
            weekday_lows_stats = weekly_zones_data.get('weekday_matching_lows_stats', {})

            # Create zone objects for H/L matching highs
            if hl_highs_stats.get('min_avg'):
                zone_objects.append(WeeklyZone(hl_highs_stats['min_avg'], 'HL MinAvg High'))
            if hl_highs_stats.get('median'):
                zone_objects.append(WeeklyZone(hl_highs_stats['median'], 'HL Median High'))
            if hl_highs_stats.get('average'):
                zone_objects.append(WeeklyZone(hl_highs_stats['average'], 'HL Average High'))
            if hl_highs_stats.get('max_avg'):
                zone_objects.append(WeeklyZone(hl_highs_stats['max_avg'], 'HL MaxAvg High'))

            # Create zone objects for H/L matching lows
            if hl_lows_stats.get('min_avg'):
                zone_objects.append(WeeklyZone(hl_lows_stats['min_avg'], 'HL MinAvg Low'))
            if hl_lows_stats.get('median'):
                zone_objects.append(WeeklyZone(hl_lows_stats['median'], 'HL Median Low'))
            if hl_lows_stats.get('average'):
                zone_objects.append(WeeklyZone(hl_lows_stats['average'], 'HL Average Low'))
            if hl_lows_stats.get('max_avg'):
                zone_objects.append(WeeklyZone(hl_lows_stats['max_avg'], 'HL MaxAvg Low'))

            # Create zone objects for weekday matching highs
            if weekday_highs_stats.get('min_avg'):
                zone_objects.append(WeeklyZone(weekday_highs_stats['min_avg'], 'Weekday MinAvg High'))
            if weekday_highs_stats.get('median'):
                zone_objects.append(WeeklyZone(weekday_highs_stats['median'], 'Weekday Median High'))
            if weekday_highs_stats.get('average'):
                zone_objects.append(WeeklyZone(weekday_highs_stats['average'], 'Weekday Average High'))
            if weekday_highs_stats.get('max_avg'):
                zone_objects.append(WeeklyZone(weekday_highs_stats['max_avg'], 'Weekday MaxAvg High'))

            # Create zone objects for weekday matching lows
            if weekday_lows_stats.get('min_avg'):
                zone_objects.append(WeeklyZone(weekday_lows_stats['min_avg'], 'Weekday MinAvg Low'))
            if weekday_lows_stats.get('median'):
                zone_objects.append(WeeklyZone(weekday_lows_stats['median'], 'Weekday Median Low'))
            if weekday_lows_stats.get('average'):
                zone_objects.append(WeeklyZone(weekday_lows_stats['average'], 'Weekday Average Low'))
            if weekday_lows_stats.get('max_avg'):
                zone_objects.append(WeeklyZone(weekday_lows_stats['max_avg'], 'Weekday MaxAvg Low'))

            return zone_objects

        except Exception as e:
            print(f"Error converting weekly zones to objects: {e}")
            return []

    def _add_weekly_zone_to_pinescript(self, lines, zone):
        """Add a single weekly zone to the PineScript lines."""
        try:
            # Determine zone color based on level name
            zone_color, text_color = self._get_weekly_zone_colors(zone)

            # Clean up level name for display
            display_name = zone.level_name

            # Weekly zones are typically lines, not boxes
            lines.append(f"// {display_name} at {zone.price:.4f}")
            lines.append("if barstate.islast")
            lines.append(f"    level_line = line.new(bar_index - 100, {zone.price:.4f}, bar_index + 100, {zone.price:.4f}, ")
            lines.append(f"                         color = {zone_color}, width = 2, extend = extend.both)")
            lines.append("    array.push(line_array, level_line)")
            lines.append(f"    level_label = label.new(bar_index + 50, {zone.price:.4f}, ")
            lines.append(f"                           text = '{display_name}: {zone.price:.4f}', style = label.style_none, ")
            lines.append(f"                           color = color.new(color.white, 100), textcolor = {text_color}, ")
            lines.append("                           size = size.small)")
            lines.append("    array.push(label_array, level_label)")
            lines.append("")

        except Exception as e:
            print(f"Error adding weekly zone to PineScript: {e}")

    def _get_weekly_zone_colors(self, zone):
        """Get appropriate colors for a weekly zone based on its properties."""
        try:
            # Default colors
            zone_color = "color.purple"
            text_color = "color.purple"

            # Color based on level name
            level_name = zone.level_name.lower()

            if 'maxavg' in level_name:
                zone_color = "color.rgb(255, 0, 255)"  # Magenta
                text_color = "color.rgb(255, 0, 255)"
            elif 'minavg' in level_name:
                zone_color = "color.rgb(128, 0, 128)"  # Dark purple
                text_color = "color.rgb(128, 0, 128)"
            elif 'average' in level_name:
                zone_color = "color.rgb(186, 85, 211)"  # Medium orchid
                text_color = "color.rgb(186, 85, 211)"
            elif 'median' in level_name:
                zone_color = "color.rgb(147, 112, 219)"  # Medium slate blue
                text_color = "color.rgb(147, 112, 219)"
            else:
                zone_color = "color.purple"
                text_color = "color.purple"

            return zone_color, text_color

        except Exception as e:
            print(f"Error getting weekly zone colors: {e}")
            return "color.purple", "color.purple"
